// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `Settings`
  String get settings {
    return Intl.message(
      'Settings',
      name: 'settings',
      desc: '',
      args: [],
    );
  }

  /// `Dark Mode`
  String get darkMode {
    return Intl.message(
      'Dark Mode',
      name: 'darkMode',
      desc: '',
      args: [],
    );
  }

  /// `Click Sounds`
  String get soundClick {
    return Intl.message(
      'Click Sounds',
      name: 'soundClick',
      desc: '',
      args: [],
    );
  }

  /// `Scroll Sounds`
  String get soundScroll {
    return Intl.message(
      'Scroll Sounds',
      name: 'soundScroll',
      desc: '',
      args: [],
    );
  }

  /// `Language`
  String get language {
    return Intl.message(
      'Language',
      name: 'language',
      desc: '',
      args: [],
    );
  }

  /// `Arabic`
  String get arabic {
    return Intl.message(
      'Arabic',
      name: 'arabic',
      desc: '',
      args: [],
    );
  }

  /// `English`
  String get english {
    return Intl.message(
      'English',
      name: 'english',
      desc: '',
      args: [],
    );
  }

  /// `Skip`
  String get skip {
    return Intl.message(
      'Skip',
      name: 'skip',
      desc: '',
      args: [],
    );
  }

  /// `Next`
  String get next {
    return Intl.message(
      'Next',
      name: 'next',
      desc: '',
      args: [],
    );
  }

  /// `Start`
  String get start {
    return Intl.message(
      'Start',
      name: 'start',
      desc: '',
      args: [],
    );
  }

  /// `Search your favorite destination...`
  String get searchHint {
    return Intl.message(
      'Search your favorite destination...',
      name: 'searchHint',
      desc: '',
      args: [],
    );
  }

  /// `Filter`
  String get filter {
    return Intl.message(
      'Filter',
      name: 'filter',
      desc: '',
      args: [],
    );
  }

  /// `Loading...`
  String get loading {
    return Intl.message(
      'Loading...',
      name: 'loading',
      desc: '',
      args: [],
    );
  }

  /// `No results found`
  String get noResults {
    return Intl.message(
      'No results found',
      name: 'noResults',
      desc: '',
      args: [],
    );
  }

  /// `SR / night`
  String get perNight {
    return Intl.message(
      'SR / night',
      name: 'perNight',
      desc: '',
      args: [],
    );
  }

  /// `Wi-Fi`
  String get wifi {
    return Intl.message(
      'Wi-Fi',
      name: 'wifi',
      desc: '',
      args: [],
    );
  }

  /// `Add to favorites`
  String get addToFavorites {
    return Intl.message(
      'Add to favorites',
      name: 'addToFavorites',
      desc: '',
      args: [],
    );
  }

  /// `Gather Point`
  String get appName {
    return Intl.message(
      'Gather Point',
      name: 'appName',
      desc: '',
      args: [],
    );
  }

  /// `Select City`
  String get selectCity {
    return Intl.message(
      'Select City',
      name: 'selectCity',
      desc: '',
      args: [],
    );
  }

  /// `Detecting location...`
  String get detectingLocation {
    return Intl.message(
      'Detecting location...',
      name: 'detectingLocation',
      desc: '',
      args: [],
    );
  }

  /// `Please enable location permission to use the app`
  String get locationPermissionError {
    return Intl.message(
      'Please enable location permission to use the app',
      name: 'locationPermissionError',
      desc: '',
      args: [],
    );
  }

  /// `Browse Reels`
  String get browseReels {
    return Intl.message(
      'Browse Reels',
      name: 'browseReels',
      desc: '',
      args: [],
    );
  }

  /// `Discover Latest Visual Content`
  String get discoverLatestVisualContent {
    return Intl.message(
      'Discover Latest Visual Content',
      name: 'discoverLatestVisualContent',
      desc: '',
      args: [],
    );
  }

  /// `Explore Categories`
  String get exploreCategories {
    return Intl.message(
      'Explore Categories',
      name: 'exploreCategories',
      desc: '',
      args: [],
    );
  }

  /// `Nearby Places`
  String get nearbyPlaces {
    return Intl.message(
      'Nearby Places',
      name: 'nearbyPlaces',
      desc: '',
      args: [],
    );
  }

  /// `Popular Destinations`
  String get popularDestinations {
    return Intl.message(
      'Popular Destinations',
      name: 'popularDestinations',
      desc: '',
      args: [],
    );
  }

  /// `Featured Places`
  String get featuredPlaces {
    return Intl.message(
      'Featured Places',
      name: 'featuredPlaces',
      desc: '',
      args: [],
    );
  }

  /// `Discover More`
  String get discoverMore {
    return Intl.message(
      'Discover More',
      name: 'discoverMore',
      desc: '',
      args: [],
    );
  }

  /// `View All`
  String get viewAll {
    return Intl.message(
      'View All',
      name: 'viewAll',
      desc: '',
      args: [],
    );
  }

  /// `Book Now`
  String get bookNow {
    return Intl.message(
      'Book Now',
      name: 'bookNow',
      desc: '',
      args: [],
    );
  }

  /// `Price per Night (SAR)`
  String get pricePerNight {
    return Intl.message(
      'Price per Night (SAR)',
      name: 'pricePerNight',
      desc: '',
      args: [],
    );
  }

  /// `Guests`
  String get guests {
    return Intl.message(
      'Guests',
      name: 'guests',
      desc: '',
      args: [],
    );
  }

  /// `Rooms`
  String get rooms {
    return Intl.message(
      'Rooms',
      name: 'rooms',
      desc: '',
      args: [],
    );
  }

  /// `Bathrooms`
  String get bathrooms {
    return Intl.message(
      'Bathrooms',
      name: 'bathrooms',
      desc: '',
      args: [],
    );
  }

  /// `Amenities`
  String get amenities {
    return Intl.message(
      'Amenities',
      name: 'amenities',
      desc: '',
      args: [],
    );
  }

  /// `Location`
  String get location {
    return Intl.message(
      'Location',
      name: 'location',
      desc: '',
      args: [],
    );
  }

  /// `Reviews`
  String get reviews {
    return Intl.message(
      'Reviews',
      name: 'reviews',
      desc: '',
      args: [],
    );
  }

  /// `Rating`
  String get rating {
    return Intl.message(
      'Rating',
      name: 'rating',
      desc: '',
      args: [],
    );
  }

  /// `Excellent`
  String get excellent {
    return Intl.message(
      'Excellent',
      name: 'excellent',
      desc: '',
      args: [],
    );
  }

  /// `Very Good`
  String get veryGood {
    return Intl.message(
      'Very Good',
      name: 'veryGood',
      desc: '',
      args: [],
    );
  }

  /// `Good`
  String get good {
    return Intl.message(
      'Good',
      name: 'good',
      desc: '',
      args: [],
    );
  }

  /// `Fair`
  String get fair {
    return Intl.message(
      'Fair',
      name: 'fair',
      desc: '',
      args: [],
    );
  }

  /// `Poor`
  String get poor {
    return Intl.message(
      'Poor',
      name: 'poor',
      desc: '',
      args: [],
    );
  }

  /// `Sound Settings`
  String get soundSettings {
    return Intl.message(
      'Sound Settings',
      name: 'soundSettings',
      desc: '',
      args: [],
    );
  }

  /// `Customize your app experience`
  String get customizeExperience {
    return Intl.message(
      'Customize your app experience',
      name: 'customizeExperience',
      desc: '',
      args: [],
    );
  }

  /// `Dark theme enabled`
  String get themeEnabled {
    return Intl.message(
      'Dark theme enabled',
      name: 'themeEnabled',
      desc: '',
      args: [],
    );
  }

  /// `Light theme enabled`
  String get themeDisabled {
    return Intl.message(
      'Light theme enabled',
      name: 'themeDisabled',
      desc: '',
      args: [],
    );
  }

  /// `Click sounds enabled`
  String get soundClickEnabled {
    return Intl.message(
      'Click sounds enabled',
      name: 'soundClickEnabled',
      desc: '',
      args: [],
    );
  }

  /// `Click sounds disabled`
  String get soundClickDisabled {
    return Intl.message(
      'Click sounds disabled',
      name: 'soundClickDisabled',
      desc: '',
      args: [],
    );
  }

  /// `Scroll sounds enabled`
  String get soundScrollEnabled {
    return Intl.message(
      'Scroll sounds enabled',
      name: 'soundScrollEnabled',
      desc: '',
      args: [],
    );
  }

  /// `Scroll sounds disabled`
  String get soundScrollDisabled {
    return Intl.message(
      'Scroll sounds disabled',
      name: 'soundScrollDisabled',
      desc: '',
      args: [],
    );
  }

  /// `Create Property`
  String get createProperty {
    return Intl.message(
      'Create Property',
      name: 'createProperty',
      desc: '',
      args: [],
    );
  }

  /// `Property Title`
  String get propertyTitle {
    return Intl.message(
      'Property Title',
      name: 'propertyTitle',
      desc: '',
      args: [],
    );
  }

  /// `Description`
  String get propertyDescription {
    return Intl.message(
      'Description',
      name: 'propertyDescription',
      desc: '',
      args: [],
    );
  }

  /// `Select Category`
  String get selectCategory {
    return Intl.message(
      'Select Category',
      name: 'selectCategory',
      desc: '',
      args: [],
    );
  }

  /// `Title & Description`
  String get titleAndDescription {
    return Intl.message(
      'Title & Description',
      name: 'titleAndDescription',
      desc: '',
      args: [],
    );
  }

  /// `Pick Location`
  String get pickLocation {
    return Intl.message(
      'Pick Location',
      name: 'pickLocation',
      desc: '',
      args: [],
    );
  }

  /// `Image Gallery`
  String get imageGallery {
    return Intl.message(
      'Image Gallery',
      name: 'imageGallery',
      desc: '',
      args: [],
    );
  }

  /// `Add Image`
  String get addImage {
    return Intl.message(
      'Add Image',
      name: 'addImage',
      desc: '',
      args: [],
    );
  }

  /// `Available Services`
  String get availableServices {
    return Intl.message(
      'Available Services',
      name: 'availableServices',
      desc: '',
      args: [],
    );
  }

  /// `Select available services`
  String get selectServices {
    return Intl.message(
      'Select available services',
      name: 'selectServices',
      desc: '',
      args: [],
    );
  }

  /// `Pricing`
  String get pricing {
    return Intl.message(
      'Pricing',
      name: 'pricing',
      desc: '',
      args: [],
    );
  }

  /// `Daily Price`
  String get dailyPrice {
    return Intl.message(
      'Daily Price',
      name: 'dailyPrice',
      desc: '',
      args: [],
    );
  }

  /// `Weekly Price`
  String get weeklyPrice {
    return Intl.message(
      'Weekly Price',
      name: 'weeklyPrice',
      desc: '',
      args: [],
    );
  }

  /// `Monthly Price`
  String get monthlyPrice {
    return Intl.message(
      'Monthly Price',
      name: 'monthlyPrice',
      desc: '',
      args: [],
    );
  }

  /// `Commission`
  String get commission {
    return Intl.message(
      'Commission',
      name: 'commission',
      desc: '',
      args: [],
    );
  }

  /// `Booking Details & Policies`
  String get bookingDetails {
    return Intl.message(
      'Booking Details & Policies',
      name: 'bookingDetails',
      desc: '',
      args: [],
    );
  }

  /// `Number of Bathrooms`
  String get numberOfBathrooms {
    return Intl.message(
      'Number of Bathrooms',
      name: 'numberOfBathrooms',
      desc: '',
      args: [],
    );
  }

  /// `Number of Bedrooms`
  String get numberOfBedrooms {
    return Intl.message(
      'Number of Bedrooms',
      name: 'numberOfBedrooms',
      desc: '',
      args: [],
    );
  }

  /// `Number of Guests`
  String get numberOfGuests {
    return Intl.message(
      'Number of Guests',
      name: 'numberOfGuests',
      desc: '',
      args: [],
    );
  }

  /// `Booking Policy`
  String get bookingPolicy {
    return Intl.message(
      'Booking Policy',
      name: 'bookingPolicy',
      desc: '',
      args: [],
    );
  }

  /// `Cancellation Policy`
  String get cancellationPolicy {
    return Intl.message(
      'Cancellation Policy',
      name: 'cancellationPolicy',
      desc: '',
      args: [],
    );
  }

  /// `Back`
  String get back {
    return Intl.message(
      'Back',
      name: 'back',
      desc: '',
      args: [],
    );
  }

  /// `Submit`
  String get submit {
    return Intl.message(
      'Submit',
      name: 'submit',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get cancel {
    return Intl.message(
      'Cancel',
      name: 'cancel',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Submission`
  String get confirmSubmission {
    return Intl.message(
      'Confirm Submission',
      name: 'confirmSubmission',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to submit this property?`
  String get confirmSubmissionMessage {
    return Intl.message(
      'Are you sure you want to submit this property?',
      name: 'confirmSubmissionMessage',
      desc: '',
      args: [],
    );
  }

  /// `Error`
  String get error {
    return Intl.message(
      'Error',
      name: 'error',
      desc: '',
      args: [],
    );
  }

  /// `OK`
  String get ok {
    return Intl.message(
      'OK',
      name: 'ok',
      desc: '',
      args: [],
    );
  }

  /// `Property created successfully`
  String get propertyCreatedSuccessfully {
    return Intl.message(
      'Property created successfully',
      name: 'propertyCreatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Tap to upload images`
  String get tapToUploadImages {
    return Intl.message(
      'Tap to upload images',
      name: 'tapToUploadImages',
      desc: '',
      args: [],
    );
  }

  /// `Latitude`
  String get latitude {
    return Intl.message(
      'Latitude',
      name: 'latitude',
      desc: '',
      args: [],
    );
  }

  /// `Longitude`
  String get longitude {
    return Intl.message(
      'Longitude',
      name: 'longitude',
      desc: '',
      args: [],
    );
  }

  /// `Enter price`
  String get enterPrice {
    return Intl.message(
      'Enter price',
      name: 'enterPrice',
      desc: '',
      args: [],
    );
  }

  /// `Failed to load categories`
  String get failedToLoadCategories {
    return Intl.message(
      'Failed to load categories',
      name: 'failedToLoadCategories',
      desc: '',
      args: [],
    );
  }

  /// `Failed to load facilities`
  String get failedToLoadFacilities {
    return Intl.message(
      'Failed to load facilities',
      name: 'failedToLoadFacilities',
      desc: '',
      args: [],
    );
  }

  /// `Failed to create item`
  String get failedToCreateItem {
    return Intl.message(
      'Failed to create item',
      name: 'failedToCreateItem',
      desc: '',
      args: [],
    );
  }

  /// `Home`
  String get home {
    return Intl.message(
      'Home',
      name: 'home',
      desc: '',
      args: [],
    );
  }

  /// `Search`
  String get search {
    return Intl.message(
      'Search',
      name: 'search',
      desc: '',
      args: [],
    );
  }

  /// `Reels`
  String get reels {
    return Intl.message(
      'Reels',
      name: 'reels',
      desc: '',
      args: [],
    );
  }

  /// `Profile`
  String get profile {
    return Intl.message(
      'Profile',
      name: 'profile',
      desc: '',
      args: [],
    );
  }

  /// `My Bookings`
  String get myBookings {
    return Intl.message(
      'My Bookings',
      name: 'myBookings',
      desc: '',
      args: [],
    );
  }

  /// `My Listings`
  String get myListings {
    return Intl.message(
      'My Listings',
      name: 'myListings',
      desc: '',
      args: [],
    );
  }

  /// `Edit Profile`
  String get editProfile {
    return Intl.message(
      'Edit Profile',
      name: 'editProfile',
      desc: '',
      args: [],
    );
  }

  /// `Personal Information`
  String get personalInfo {
    return Intl.message(
      'Personal Information',
      name: 'personalInfo',
      desc: '',
      args: [],
    );
  }

  /// `Contact Information`
  String get contactInfo {
    return Intl.message(
      'Contact Information',
      name: 'contactInfo',
      desc: '',
      args: [],
    );
  }

  /// `Additional Information`
  String get additionalInfo {
    return Intl.message(
      'Additional Information',
      name: 'additionalInfo',
      desc: '',
      args: [],
    );
  }

  /// `Full Name`
  String get fullName {
    return Intl.message(
      'Full Name',
      name: 'fullName',
      desc: '',
      args: [],
    );
  }

  /// `Bio`
  String get bio {
    return Intl.message(
      'Bio',
      name: 'bio',
      desc: '',
      args: [],
    );
  }

  /// `Email`
  String get email {
    return Intl.message(
      'Email',
      name: 'email',
      desc: '',
      args: [],
    );
  }

  /// `Phone`
  String get phone {
    return Intl.message(
      'Phone',
      name: 'phone',
      desc: '',
      args: [],
    );
  }

  /// `Gender`
  String get gender {
    return Intl.message(
      'Gender',
      name: 'gender',
      desc: '',
      args: [],
    );
  }

  /// `Birthdate`
  String get birthdate {
    return Intl.message(
      'Birthdate',
      name: 'birthdate',
      desc: '',
      args: [],
    );
  }

  /// `Male`
  String get male {
    return Intl.message(
      'Male',
      name: 'male',
      desc: '',
      args: [],
    );
  }

  /// `Female`
  String get female {
    return Intl.message(
      'Female',
      name: 'female',
      desc: '',
      args: [],
    );
  }

  /// `Not Specified`
  String get notSpecified {
    return Intl.message(
      'Not Specified',
      name: 'notSpecified',
      desc: '',
      args: [],
    );
  }

  /// `Select Birthdate`
  String get selectBirthdate {
    return Intl.message(
      'Select Birthdate',
      name: 'selectBirthdate',
      desc: '',
      args: [],
    );
  }

  /// `Save Changes`
  String get saveChanges {
    return Intl.message(
      'Save Changes',
      name: 'saveChanges',
      desc: '',
      args: [],
    );
  }

  /// `Profile Image`
  String get profileImage {
    return Intl.message(
      'Profile Image',
      name: 'profileImage',
      desc: '',
      args: [],
    );
  }

  /// `Tap to change image`
  String get tapToChangeImage {
    return Intl.message(
      'Tap to change image',
      name: 'tapToChangeImage',
      desc: '',
      args: [],
    );
  }

  /// `Total Bookings`
  String get totalBookings {
    return Intl.message(
      'Total Bookings',
      name: 'totalBookings',
      desc: '',
      args: [],
    );
  }

  /// `Confirmed Bookings`
  String get confirmedBookings {
    return Intl.message(
      'Confirmed Bookings',
      name: 'confirmedBookings',
      desc: '',
      args: [],
    );
  }

  /// `Total Properties`
  String get totalProperties {
    return Intl.message(
      'Total Properties',
      name: 'totalProperties',
      desc: '',
      args: [],
    );
  }

  /// `Total Views`
  String get totalViews {
    return Intl.message(
      'Total Views',
      name: 'totalViews',
      desc: '',
      args: [],
    );
  }

  /// `Total Reservations`
  String get totalReservations {
    return Intl.message(
      'Total Reservations',
      name: 'totalReservations',
      desc: '',
      args: [],
    );
  }

  /// `All`
  String get all {
    return Intl.message(
      'All',
      name: 'all',
      desc: '',
      args: [],
    );
  }

  /// `Confirmed`
  String get confirmed {
    return Intl.message(
      'Confirmed',
      name: 'confirmed',
      desc: '',
      args: [],
    );
  }

  /// `Pending`
  String get pending {
    return Intl.message(
      'Pending',
      name: 'pending',
      desc: '',
      args: [],
    );
  }

  /// `Completed`
  String get completed {
    return Intl.message(
      'Completed',
      name: 'completed',
      desc: '',
      args: [],
    );
  }

  /// `Cancelled`
  String get cancelled {
    return Intl.message(
      'Cancelled',
      name: 'cancelled',
      desc: '',
      args: [],
    );
  }

  /// `Active`
  String get active {
    return Intl.message(
      'Active',
      name: 'active',
      desc: '',
      args: [],
    );
  }

  /// `Inactive`
  String get inactive {
    return Intl.message(
      'Inactive',
      name: 'inactive',
      desc: '',
      args: [],
    );
  }

  /// `Under Review`
  String get underReview {
    return Intl.message(
      'Under Review',
      name: 'underReview',
      desc: '',
      args: [],
    );
  }

  /// `Check In`
  String get checkIn {
    return Intl.message(
      'Check In',
      name: 'checkIn',
      desc: '',
      args: [],
    );
  }

  /// `Check Out`
  String get checkOut {
    return Intl.message(
      'Check Out',
      name: 'checkOut',
      desc: '',
      args: [],
    );
  }

  /// `Total Price`
  String get totalPrice {
    return Intl.message(
      'Total Price',
      name: 'totalPrice',
      desc: '',
      args: [],
    );
  }

  /// `View Details`
  String get viewDetails {
    return Intl.message(
      'View Details',
      name: 'viewDetails',
      desc: '',
      args: [],
    );
  }

  /// `Cancel Booking`
  String get cancelBooking {
    return Intl.message(
      'Cancel Booking',
      name: 'cancelBooking',
      desc: '',
      args: [],
    );
  }

  /// `Rebook Property`
  String get rebookProperty {
    return Intl.message(
      'Rebook Property',
      name: 'rebookProperty',
      desc: '',
      args: [],
    );
  }

  /// `Edit Property`
  String get editProperty {
    return Intl.message(
      'Edit Property',
      name: 'editProperty',
      desc: '',
      args: [],
    );
  }

  /// `Bedrooms`
  String get bedrooms {
    return Intl.message(
      'Bedrooms',
      name: 'bedrooms',
      desc: '',
      args: [],
    );
  }

  /// `Property Details`
  String get propertyDetails {
    return Intl.message(
      'Property Details',
      name: 'propertyDetails',
      desc: '',
      args: [],
    );
  }

  /// `Host Mode`
  String get hostMode {
    return Intl.message(
      'Host Mode',
      name: 'hostMode',
      desc: '',
      args: [],
    );
  }

  /// `Enable host mode to manage your properties`
  String get enableHostMode {
    return Intl.message(
      'Enable host mode to manage your properties',
      name: 'enableHostMode',
      desc: '',
      args: [],
    );
  }

  /// `Logout`
  String get logout {
    return Intl.message(
      'Logout',
      name: 'logout',
      desc: '',
      args: [],
    );
  }

  /// `Appearance`
  String get appearance {
    return Intl.message(
      'Appearance',
      name: 'appearance',
      desc: '',
      args: [],
    );
  }

  /// `Notifications`
  String get notifications {
    return Intl.message(
      'Notifications',
      name: 'notifications',
      desc: '',
      args: [],
    );
  }

  /// `Privacy & Security`
  String get privacy {
    return Intl.message(
      'Privacy & Security',
      name: 'privacy',
      desc: '',
      args: [],
    );
  }

  /// `About App`
  String get about {
    return Intl.message(
      'About App',
      name: 'about',
      desc: '',
      args: [],
    );
  }

  /// `Manage notification settings`
  String get manageNotifications {
    return Intl.message(
      'Manage notification settings',
      name: 'manageNotifications',
      desc: '',
      args: [],
    );
  }

  /// `Privacy and security settings`
  String get privacySettings {
    return Intl.message(
      'Privacy and security settings',
      name: 'privacySettings',
      desc: '',
      args: [],
    );
  }

  /// `App information and version`
  String get appInfo {
    return Intl.message(
      'App information and version',
      name: 'appInfo',
      desc: '',
      args: [],
    );
  }

  /// `Version`
  String get version {
    return Intl.message(
      'Version',
      name: 'version',
      desc: '',
      args: [],
    );
  }

  /// `Property booking and rental app`
  String get appDescription {
    return Intl.message(
      'Property booking and rental app',
      name: 'appDescription',
      desc: '',
      args: [],
    );
  }

  /// `No bookings yet`
  String get noBookingsYet {
    return Intl.message(
      'No bookings yet',
      name: 'noBookingsYet',
      desc: '',
      args: [],
    );
  }

  /// `You haven't made any bookings yet`
  String get noBookingsSubtitle {
    return Intl.message(
      'You haven\'t made any bookings yet',
      name: 'noBookingsSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Explore Properties`
  String get exploreProperties {
    return Intl.message(
      'Explore Properties',
      name: 'exploreProperties',
      desc: '',
      args: [],
    );
  }

  /// `No properties yet`
  String get noPropertiesYet {
    return Intl.message(
      'No properties yet',
      name: 'noPropertiesYet',
      desc: '',
      args: [],
    );
  }

  /// `Start by adding your first property`
  String get noPropertiesSubtitle {
    return Intl.message(
      'Start by adding your first property',
      name: 'noPropertiesSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Add Property`
  String get addProperty {
    return Intl.message(
      'Add Property',
      name: 'addProperty',
      desc: '',
      args: [],
    );
  }

  /// `Searching...`
  String get searching {
    return Intl.message(
      'Searching...',
      name: 'searching',
      desc: '',
      args: [],
    );
  }

  /// `Try searching with different keywords`
  String get tryDifferentKeywords {
    return Intl.message(
      'Try searching with different keywords',
      name: 'tryDifferentKeywords',
      desc: '',
      args: [],
    );
  }

  /// `Back to Search`
  String get backToSearch {
    return Intl.message(
      'Back to Search',
      name: 'backToSearch',
      desc: '',
      args: [],
    );
  }

  /// `Loading reels...`
  String get loadingReels {
    return Intl.message(
      'Loading reels...',
      name: 'loadingReels',
      desc: '',
      args: [],
    );
  }

  /// `Failed to load reels`
  String get failedToLoadReels {
    return Intl.message(
      'Failed to load reels',
      name: 'failedToLoadReels',
      desc: '',
      args: [],
    );
  }

  /// `Check your internet connection`
  String get checkConnection {
    return Intl.message(
      'Check your internet connection',
      name: 'checkConnection',
      desc: '',
      args: [],
    );
  }

  /// `Retry`
  String get retry {
    return Intl.message(
      'Retry',
      name: 'retry',
      desc: '',
      args: [],
    );
  }

  /// `Share Property`
  String get shareProperty {
    return Intl.message(
      'Share Property',
      name: 'shareProperty',
      desc: '',
      args: [],
    );
  }

  /// `About this place`
  String get aboutThisPlace {
    return Intl.message(
      'About this place',
      name: 'aboutThisPlace',
      desc: '',
      args: [],
    );
  }

  /// `What this place offers`
  String get whatThisPlaceOffers {
    return Intl.message(
      'What this place offers',
      name: 'whatThisPlaceOffers',
      desc: '',
      args: [],
    );
  }

  /// `Where you'll be`
  String get whereYoullBe {
    return Intl.message(
      'Where you\'ll be',
      name: 'whereYoullBe',
      desc: '',
      args: [],
    );
  }

  /// `Hosted by`
  String get hostedBy {
    return Intl.message(
      'Hosted by',
      name: 'hostedBy',
      desc: '',
      args: [],
    );
  }

  /// `Superhost`
  String get superhost {
    return Intl.message(
      'Superhost',
      name: 'superhost',
      desc: '',
      args: [],
    );
  }

  /// `years hosting`
  String get yearsHosting {
    return Intl.message(
      'years hosting',
      name: 'yearsHosting',
      desc: '',
      args: [],
    );
  }

  /// `Since`
  String get since {
    return Intl.message(
      'Since',
      name: 'since',
      desc: '',
      args: [],
    );
  }

  /// `Change`
  String get change {
    return Intl.message(
      'Change',
      name: 'change',
      desc: '',
      args: [],
    );
  }

  /// `Show more`
  String get showMore {
    return Intl.message(
      'Show more',
      name: 'showMore',
      desc: '',
      args: [],
    );
  }

  /// `Show less`
  String get showLess {
    return Intl.message(
      'Show less',
      name: 'showLess',
      desc: '',
      args: [],
    );
  }

  /// `No amenities listed`
  String get noAmenitiesListed {
    return Intl.message(
      'No amenities listed',
      name: 'noAmenitiesListed',
      desc: '',
      args: [],
    );
  }

  /// `Show all amenities`
  String get showAllAmenities {
    return Intl.message(
      'Show all amenities',
      name: 'showAllAmenities',
      desc: '',
      args: [],
    );
  }

  /// `Map view`
  String get mapView {
    return Intl.message(
      'Map view',
      name: 'mapView',
      desc: '',
      args: [],
    );
  }

  /// `Guest Review`
  String get guestReview {
    return Intl.message(
      'Guest Review',
      name: 'guestReview',
      desc: '',
      args: [],
    );
  }

  /// `Reserve`
  String get reserve {
    return Intl.message(
      'Reserve',
      name: 'reserve',
      desc: '',
      args: [],
    );
  }

  /// `Booking fee`
  String get bookingFee {
    return Intl.message(
      'Booking fee',
      name: 'bookingFee',
      desc: '',
      args: [],
    );
  }

  /// `Service fee`
  String get serviceFee {
    return Intl.message(
      'Service fee',
      name: 'serviceFee',
      desc: '',
      args: [],
    );
  }

  /// `Taxes`
  String get taxes {
    return Intl.message(
      'Taxes',
      name: 'taxes',
      desc: '',
      args: [],
    );
  }

  /// `Total`
  String get total {
    return Intl.message(
      'Total',
      name: 'total',
      desc: '',
      args: [],
    );
  }

  /// `Current Location`
  String get currentLocation {
    return Intl.message(
      'Current Location',
      name: 'currentLocation',
      desc: '',
      args: [],
    );
  }

  /// `Welcome... search for what you want`
  String get searchPlaceholder {
    return Intl.message(
      'Welcome... search for what you want',
      name: 'searchPlaceholder',
      desc: '',
      args: [],
    );
  }

  /// `Categories`
  String get categories {
    return Intl.message(
      'Categories',
      name: 'categories',
      desc: '',
      args: [],
    );
  }

  /// `Popular Places`
  String get popularPlaces {
    return Intl.message(
      'Popular Places',
      name: 'popularPlaces',
      desc: '',
      args: [],
    );
  }

  /// `See All`
  String get seeAll {
    return Intl.message(
      'See All',
      name: 'seeAll',
      desc: '',
      args: [],
    );
  }

  /// `Views`
  String get views {
    return Intl.message(
      'Views',
      name: 'views',
      desc: '',
      args: [],
    );
  }

  /// `Properties`
  String get properties {
    return Intl.message(
      'Properties',
      name: 'properties',
      desc: '',
      args: [],
    );
  }

  /// `Reservations`
  String get reservations {
    return Intl.message(
      'Reservations',
      name: 'reservations',
      desc: '',
      args: [],
    );
  }

  /// `Status`
  String get status {
    return Intl.message(
      'Status',
      name: 'status',
      desc: '',
      args: [],
    );
  }

  /// `Price`
  String get price {
    return Intl.message(
      'Price',
      name: 'price',
      desc: '',
      args: [],
    );
  }

  /// `Check-in Date`
  String get checkInDate {
    return Intl.message(
      'Check-in Date',
      name: 'checkInDate',
      desc: '',
      args: [],
    );
  }

  /// `Check-out Date`
  String get checkOutDate {
    return Intl.message(
      'Check-out Date',
      name: 'checkOutDate',
      desc: '',
      args: [],
    );
  }

  /// `Property Name`
  String get propertyName {
    return Intl.message(
      'Property Name',
      name: 'propertyName',
      desc: '',
      args: [],
    );
  }

  /// `Property Location`
  String get propertyLocation {
    return Intl.message(
      'Property Location',
      name: 'propertyLocation',
      desc: '',
      args: [],
    );
  }

  /// `Total Amount`
  String get totalAmount {
    return Intl.message(
      'Total Amount',
      name: 'totalAmount',
      desc: '',
      args: [],
    );
  }

  /// `Booking Status`
  String get bookingStatus {
    return Intl.message(
      'Booking Status',
      name: 'bookingStatus',
      desc: '',
      args: [],
    );
  }

  /// `Booking Date`
  String get bookingDate {
    return Intl.message(
      'Booking Date',
      name: 'bookingDate',
      desc: '',
      args: [],
    );
  }

  /// `Host Name`
  String get hostName {
    return Intl.message(
      'Host Name',
      name: 'hostName',
      desc: '',
      args: [],
    );
  }

  /// `Contact Host`
  String get contactHost {
    return Intl.message(
      'Contact Host',
      name: 'contactHost',
      desc: '',
      args: [],
    );
  }

  /// `Cancel Reservation`
  String get cancelReservation {
    return Intl.message(
      'Cancel Reservation',
      name: 'cancelReservation',
      desc: '',
      args: [],
    );
  }

  /// `Modify Reservation`
  String get modifyReservation {
    return Intl.message(
      'Modify Reservation',
      name: 'modifyReservation',
      desc: '',
      args: [],
    );
  }

  /// `Leave Review`
  String get leaveReview {
    return Intl.message(
      'Leave Review',
      name: 'leaveReview',
      desc: '',
      args: [],
    );
  }

  /// `Download Receipt`
  String get downloadReceipt {
    return Intl.message(
      'Download Receipt',
      name: 'downloadReceipt',
      desc: '',
      args: [],
    );
  }

  /// `Property Type`
  String get propertyType {
    return Intl.message(
      'Property Type',
      name: 'propertyType',
      desc: '',
      args: [],
    );
  }

  /// `Description`
  String get description {
    return Intl.message(
      'Description',
      name: 'description',
      desc: '',
      args: [],
    );
  }

  /// `Rules`
  String get rules {
    return Intl.message(
      'Rules',
      name: 'rules',
      desc: '',
      args: [],
    );
  }

  /// `Safety Features`
  String get safetyFeatures {
    return Intl.message(
      'Safety Features',
      name: 'safetyFeatures',
      desc: '',
      args: [],
    );
  }

  /// `Accessibility`
  String get accessibility {
    return Intl.message(
      'Accessibility',
      name: 'accessibility',
      desc: '',
      args: [],
    );
  }

  /// `Nearby Attractions`
  String get nearbyAttractions {
    return Intl.message(
      'Nearby Attractions',
      name: 'nearbyAttractions',
      desc: '',
      args: [],
    );
  }

  /// `Transportation`
  String get transportation {
    return Intl.message(
      'Transportation',
      name: 'transportation',
      desc: '',
      args: [],
    );
  }

  /// `Check-in Instructions`
  String get checkInInstructions {
    return Intl.message(
      'Check-in Instructions',
      name: 'checkInInstructions',
      desc: '',
      args: [],
    );
  }

  /// `House Rules`
  String get houseRules {
    return Intl.message(
      'House Rules',
      name: 'houseRules',
      desc: '',
      args: [],
    );
  }

  /// `Important Information`
  String get importantInfo {
    return Intl.message(
      'Important Information',
      name: 'importantInfo',
      desc: '',
      args: [],
    );
  }

  /// `Instant Book`
  String get instantBook {
    return Intl.message(
      'Instant Book',
      name: 'instantBook',
      desc: '',
      args: [],
    );
  }

  /// `Verified`
  String get verified {
    return Intl.message(
      'Verified',
      name: 'verified',
      desc: '',
      args: [],
    );
  }

  /// `New Listing`
  String get newListing {
    return Intl.message(
      'New Listing',
      name: 'newListing',
      desc: '',
      args: [],
    );
  }

  /// `Rare Find`
  String get rareFind {
    return Intl.message(
      'Rare Find',
      name: 'rareFind',
      desc: '',
      args: [],
    );
  }

  /// `Guest Favorite`
  String get guestFavorite {
    return Intl.message(
      'Guest Favorite',
      name: 'guestFavorite',
      desc: '',
      args: [],
    );
  }

  /// `Top Rated`
  String get topRated {
    return Intl.message(
      'Top Rated',
      name: 'topRated',
      desc: '',
      args: [],
    );
  }

  /// `Luxury Stay`
  String get luxuryStay {
    return Intl.message(
      'Luxury Stay',
      name: 'luxuryStay',
      desc: '',
      args: [],
    );
  }

  /// `Budget Friendly`
  String get budgetFriendly {
    return Intl.message(
      'Budget Friendly',
      name: 'budgetFriendly',
      desc: '',
      args: [],
    );
  }

  /// `Family Friendly`
  String get familyFriendly {
    return Intl.message(
      'Family Friendly',
      name: 'familyFriendly',
      desc: '',
      args: [],
    );
  }

  /// `Pet Friendly`
  String get petFriendly {
    return Intl.message(
      'Pet Friendly',
      name: 'petFriendly',
      desc: '',
      args: [],
    );
  }

  /// `Work Friendly`
  String get workFriendly {
    return Intl.message(
      'Work Friendly',
      name: 'workFriendly',
      desc: '',
      args: [],
    );
  }

  /// `Party Friendly`
  String get partyFriendly {
    return Intl.message(
      'Party Friendly',
      name: 'partyFriendly',
      desc: '',
      args: [],
    );
  }

  /// `Smoking Allowed`
  String get smokingAllowed {
    return Intl.message(
      'Smoking Allowed',
      name: 'smokingAllowed',
      desc: '',
      args: [],
    );
  }

  /// `No Smoking`
  String get noSmoking {
    return Intl.message(
      'No Smoking',
      name: 'noSmoking',
      desc: '',
      args: [],
    );
  }

  /// `Free WiFi`
  String get freeWifi {
    return Intl.message(
      'Free WiFi',
      name: 'freeWifi',
      desc: '',
      args: [],
    );
  }

  /// `Free Parking`
  String get freeParking {
    return Intl.message(
      'Free Parking',
      name: 'freeParking',
      desc: '',
      args: [],
    );
  }

  /// `Pool`
  String get pool {
    return Intl.message(
      'Pool',
      name: 'pool',
      desc: '',
      args: [],
    );
  }

  /// `Gym`
  String get gym {
    return Intl.message(
      'Gym',
      name: 'gym',
      desc: '',
      args: [],
    );
  }

  /// `Spa`
  String get spa {
    return Intl.message(
      'Spa',
      name: 'spa',
      desc: '',
      args: [],
    );
  }

  /// `Restaurant`
  String get restaurant {
    return Intl.message(
      'Restaurant',
      name: 'restaurant',
      desc: '',
      args: [],
    );
  }

  /// `Bar`
  String get bar {
    return Intl.message(
      'Bar',
      name: 'bar',
      desc: '',
      args: [],
    );
  }

  /// `Laundry`
  String get laundry {
    return Intl.message(
      'Laundry',
      name: 'laundry',
      desc: '',
      args: [],
    );
  }

  /// `Kitchen`
  String get kitchen {
    return Intl.message(
      'Kitchen',
      name: 'kitchen',
      desc: '',
      args: [],
    );
  }

  /// `Air Conditioning`
  String get airConditioning {
    return Intl.message(
      'Air Conditioning',
      name: 'airConditioning',
      desc: '',
      args: [],
    );
  }

  /// `Heating`
  String get heating {
    return Intl.message(
      'Heating',
      name: 'heating',
      desc: '',
      args: [],
    );
  }

  /// `TV`
  String get tv {
    return Intl.message(
      'TV',
      name: 'tv',
      desc: '',
      args: [],
    );
  }

  /// `Workspace`
  String get workspace {
    return Intl.message(
      'Workspace',
      name: 'workspace',
      desc: '',
      args: [],
    );
  }

  /// `Balcony`
  String get balcony {
    return Intl.message(
      'Balcony',
      name: 'balcony',
      desc: '',
      args: [],
    );
  }

  /// `Garden`
  String get garden {
    return Intl.message(
      'Garden',
      name: 'garden',
      desc: '',
      args: [],
    );
  }

  /// `Beach Access`
  String get beachAccess {
    return Intl.message(
      'Beach Access',
      name: 'beachAccess',
      desc: '',
      args: [],
    );
  }

  /// `Mountain View`
  String get mountainView {
    return Intl.message(
      'Mountain View',
      name: 'mountainView',
      desc: '',
      args: [],
    );
  }

  /// `City View`
  String get cityView {
    return Intl.message(
      'City View',
      name: 'cityView',
      desc: '',
      args: [],
    );
  }

  /// `Ocean View`
  String get oceanView {
    return Intl.message(
      'Ocean View',
      name: 'oceanView',
      desc: '',
      args: [],
    );
  }

  /// `Lake View`
  String get lakeView {
    return Intl.message(
      'Lake View',
      name: 'lakeView',
      desc: '',
      args: [],
    );
  }

  /// `Garden View`
  String get gardenView {
    return Intl.message(
      'Garden View',
      name: 'gardenView',
      desc: '',
      args: [],
    );
  }

  /// `Street View`
  String get streetView {
    return Intl.message(
      'Street View',
      name: 'streetView',
      desc: '',
      args: [],
    );
  }

  /// `No View`
  String get noView {
    return Intl.message(
      'No View',
      name: 'noView',
      desc: '',
      args: [],
    );
  }

  /// `Booking Summary`
  String get bookingSummary {
    return Intl.message(
      'Booking Summary',
      name: 'bookingSummary',
      desc: '',
      args: [],
    );
  }

  /// `Host Dashboard`
  String get hostDashboard {
    return Intl.message(
      'Host Dashboard',
      name: 'hostDashboard',
      desc: '',
      args: [],
    );
  }

  /// `Wallet Balance`
  String get walletBalance {
    return Intl.message(
      'Wallet Balance',
      name: 'walletBalance',
      desc: '',
      args: [],
    );
  }

  /// `Total Earnings`
  String get totalEarnings {
    return Intl.message(
      'Total Earnings',
      name: 'totalEarnings',
      desc: '',
      args: [],
    );
  }

  /// `Recent Bookings`
  String get recentBookings {
    return Intl.message(
      'Recent Bookings',
      name: 'recentBookings',
      desc: '',
      args: [],
    );
  }

  /// `Recent Reviews`
  String get recentReviews {
    return Intl.message(
      'Recent Reviews',
      name: 'recentReviews',
      desc: '',
      args: [],
    );
  }

  /// `Withdraw`
  String get withdraw {
    return Intl.message(
      'Withdraw',
      name: 'withdraw',
      desc: '',
      args: [],
    );
  }

  /// `Earnings`
  String get earnings {
    return Intl.message(
      'Earnings',
      name: 'earnings',
      desc: '',
      args: [],
    );
  }

  /// `Bookings Chart`
  String get bookingsChart {
    return Intl.message(
      'Bookings Chart',
      name: 'bookingsChart',
      desc: '',
      args: [],
    );
  }

  /// `Earnings Chart`
  String get earningsChart {
    return Intl.message(
      'Earnings Chart',
      name: 'earningsChart',
      desc: '',
      args: [],
    );
  }

  /// `This Month`
  String get thisMonth {
    return Intl.message(
      'This Month',
      name: 'thisMonth',
      desc: '',
      args: [],
    );
  }

  /// `Last Month`
  String get lastMonth {
    return Intl.message(
      'Last Month',
      name: 'lastMonth',
      desc: '',
      args: [],
    );
  }

  /// `This Year`
  String get thisYear {
    return Intl.message(
      'This Year',
      name: 'thisYear',
      desc: '',
      args: [],
    );
  }

  /// `Available Balance`
  String get availableBalance {
    return Intl.message(
      'Available Balance',
      name: 'availableBalance',
      desc: '',
      args: [],
    );
  }

  /// `Pending Earnings`
  String get pendingEarnings {
    return Intl.message(
      'Pending Earnings',
      name: 'pendingEarnings',
      desc: '',
      args: [],
    );
  }

  /// `Total Withdrawn`
  String get totalWithdrawn {
    return Intl.message(
      'Total Withdrawn',
      name: 'totalWithdrawn',
      desc: '',
      args: [],
    );
  }

  /// `Withdraw Funds`
  String get withdrawFunds {
    return Intl.message(
      'Withdraw Funds',
      name: 'withdrawFunds',
      desc: '',
      args: [],
    );
  }

  /// `Enter Amount`
  String get enterAmount {
    return Intl.message(
      'Enter Amount',
      name: 'enterAmount',
      desc: '',
      args: [],
    );
  }

  /// `Minimum withdrawal: SR 50`
  String get minimumWithdraw {
    return Intl.message(
      'Minimum withdrawal: SR 50',
      name: 'minimumWithdraw',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal Method`
  String get withdrawalMethod {
    return Intl.message(
      'Withdrawal Method',
      name: 'withdrawalMethod',
      desc: '',
      args: [],
    );
  }

  /// `Bank Transfer`
  String get bankTransfer {
    return Intl.message(
      'Bank Transfer',
      name: 'bankTransfer',
      desc: '',
      args: [],
    );
  }

  /// `PayPal`
  String get paypal {
    return Intl.message(
      'PayPal',
      name: 'paypal',
      desc: '',
      args: [],
    );
  }

  /// `Processing`
  String get processing {
    return Intl.message(
      'Processing',
      name: 'processing',
      desc: '',
      args: [],
    );
  }

  /// `No recent bookings`
  String get noRecentBookings {
    return Intl.message(
      'No recent bookings',
      name: 'noRecentBookings',
      desc: '',
      args: [],
    );
  }

  /// `No recent reviews`
  String get noRecentReviews {
    return Intl.message(
      'No recent reviews',
      name: 'noRecentReviews',
      desc: '',
      args: [],
    );
  }

  /// `Average`
  String get average {
    return Intl.message(
      'Average',
      name: 'average',
      desc: '',
      args: [],
    );
  }

  /// `Guest Name`
  String get guestName {
    return Intl.message(
      'Guest Name',
      name: 'guestName',
      desc: '',
      args: [],
    );
  }

  /// `Check-in/out`
  String get checkInOut {
    return Intl.message(
      'Check-in/out',
      name: 'checkInOut',
      desc: '',
      args: [],
    );
  }

  /// `Nights Stayed`
  String get nightsStayed {
    return Intl.message(
      'Nights Stayed',
      name: 'nightsStayed',
      desc: '',
      args: [],
    );
  }

  /// `Earnings Overview`
  String get earningsOverview {
    return Intl.message(
      'Earnings Overview',
      name: 'earningsOverview',
      desc: '',
      args: [],
    );
  }

  /// `Bookings Overview`
  String get bookingsOverview {
    return Intl.message(
      'Bookings Overview',
      name: 'bookingsOverview',
      desc: '',
      args: [],
    );
  }

  /// `Last 6 Months`
  String get last6Months {
    return Intl.message(
      'Last 6 Months',
      name: 'last6Months',
      desc: '',
      args: [],
    );
  }

  /// `Guest Comment`
  String get guestComment {
    return Intl.message(
      'Guest Comment',
      name: 'guestComment',
      desc: '',
      args: [],
    );
  }

  /// `Dates`
  String get dates {
    return Intl.message(
      'Dates',
      name: 'dates',
      desc: '',
      args: [],
    );
  }

  /// `Nights`
  String get nights {
    return Intl.message(
      'Nights',
      name: 'nights',
      desc: '',
      args: [],
    );
  }

  /// `No data available`
  String get noData {
    return Intl.message(
      'No data available',
      name: 'noData',
      desc: '',
      args: [],
    );
  }

  /// `Enter search term...`
  String get enterSearchTerm {
    return Intl.message(
      'Enter search term...',
      name: 'enterSearchTerm',
      desc: '',
      args: [],
    );
  }

  /// `No results found`
  String get noSearchResults {
    return Intl.message(
      'No results found',
      name: 'noSearchResults',
      desc: '',
      args: [],
    );
  }

  /// `No title`
  String get noTitle {
    return Intl.message(
      'No title',
      name: 'noTitle',
      desc: '',
      args: [],
    );
  }

  /// `No description`
  String get noDescription {
    return Intl.message(
      'No description',
      name: 'noDescription',
      desc: '',
      args: [],
    );
  }

  /// `Try searching with different words`
  String get tryDifferentSearch {
    return Intl.message(
      'Try searching with different words',
      name: 'tryDifferentSearch',
      desc: '',
      args: [],
    );
  }

  /// `Search Results`
  String get searchResults {
    return Intl.message(
      'Search Results',
      name: 'searchResults',
      desc: '',
      args: [],
    );
  }

  /// `No recent bookings`
  String get noBookingsMessage {
    return Intl.message(
      'No recent bookings',
      name: 'noBookingsMessage',
      desc: '',
      args: [],
    );
  }

  /// `No recent reviews`
  String get noReviewsMessage {
    return Intl.message(
      'No recent reviews',
      name: 'noReviewsMessage',
      desc: '',
      args: [],
    );
  }

  /// `Enable host mode to manage your properties`
  String get hostModeDescription {
    return Intl.message(
      'Enable host mode to manage your properties',
      name: 'hostModeDescription',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to logout?`
  String get logoutConfirmation {
    return Intl.message(
      'Are you sure you want to logout?',
      name: 'logoutConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `You must login`
  String get mustLogin {
    return Intl.message(
      'You must login',
      name: 'mustLogin',
      desc: '',
      args: [],
    );
  }

  /// `Please login to view your profile`
  String get mustLoginDescription {
    return Intl.message(
      'Please login to view your profile',
      name: 'mustLoginDescription',
      desc: '',
      args: [],
    );
  }

  /// `Login`
  String get login {
    return Intl.message(
      'Login',
      name: 'login',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Reservation`
  String get confirmReservation {
    return Intl.message(
      'Confirm Reservation',
      name: 'confirmReservation',
      desc: '',
      args: [],
    );
  }

  /// `Unit Details`
  String get unitDetails {
    return Intl.message(
      'Unit Details',
      name: 'unitDetails',
      desc: '',
      args: [],
    );
  }

  /// `Unit Name`
  String get unitName {
    return Intl.message(
      'Unit Name',
      name: 'unitName',
      desc: '',
      args: [],
    );
  }

  /// `Number of Days`
  String get numberOfDays {
    return Intl.message(
      'Number of Days',
      name: 'numberOfDays',
      desc: '',
      args: [],
    );
  }

  /// `Reservation From`
  String get reservationFrom {
    return Intl.message(
      'Reservation From',
      name: 'reservationFrom',
      desc: '',
      args: [],
    );
  }

  /// `Reservation To`
  String get reservationTo {
    return Intl.message(
      'Reservation To',
      name: 'reservationTo',
      desc: '',
      args: [],
    );
  }

  /// `Price Details`
  String get priceDetails {
    return Intl.message(
      'Price Details',
      name: 'priceDetails',
      desc: '',
      args: [],
    );
  }

  /// `Final Price`
  String get finalPrice {
    return Intl.message(
      'Final Price',
      name: 'finalPrice',
      desc: '',
      args: [],
    );
  }

  /// `Price Breakdown`
  String get priceBredown {
    return Intl.message(
      'Price Breakdown',
      name: 'priceBredown',
      desc: '',
      args: [],
    );
  }

  /// `Price Type`
  String get priceType {
    return Intl.message(
      'Price Type',
      name: 'priceType',
      desc: '',
      args: [],
    );
  }

  /// `Weekend Price`
  String get weekendPrice {
    return Intl.message(
      'Weekend Price',
      name: 'weekendPrice',
      desc: '',
      args: [],
    );
  }

  /// `Normal Days`
  String get normalDays {
    return Intl.message(
      'Normal Days',
      name: 'normalDays',
      desc: '',
      args: [],
    );
  }

  /// `Weekend Days`
  String get weekendDays {
    return Intl.message(
      'Weekend Days',
      name: 'weekendDays',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Booking`
  String get confirmBooking {
    return Intl.message(
      'Confirm Booking',
      name: 'confirmBooking',
      desc: '',
      args: [],
    );
  }

  /// `Reservation confirmed successfully!`
  String get reservationConfirmed {
    return Intl.message(
      'Reservation confirmed successfully!',
      name: 'reservationConfirmed',
      desc: '',
      args: [],
    );
  }

  /// `Failed to confirm reservation`
  String get reservationFailed {
    return Intl.message(
      'Failed to confirm reservation',
      name: 'reservationFailed',
      desc: '',
      args: [],
    );
  }

  /// `Invalid date`
  String get invalidDate {
    return Intl.message(
      'Invalid date',
      name: 'invalidDate',
      desc: '',
      args: [],
    );
  }

  /// `Not available`
  String get notAvailable {
    return Intl.message(
      'Not available',
      name: 'notAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Days`
  String get days {
    return Intl.message(
      'Days',
      name: 'days',
      desc: '',
      args: [],
    );
  }

  /// `Reviews Overview`
  String get reviewsOverview {
    return Intl.message(
      'Reviews Overview',
      name: 'reviewsOverview',
      desc: '',
      args: [],
    );
  }

  /// `Total Reviews`
  String get totalReviews {
    return Intl.message(
      'Total Reviews',
      name: 'totalReviews',
      desc: '',
      args: [],
    );
  }

  /// `All`
  String get allReviews {
    return Intl.message(
      'All',
      name: 'allReviews',
      desc: '',
      args: [],
    );
  }

  /// `High Rated`
  String get highRated {
    return Intl.message(
      'High Rated',
      name: 'highRated',
      desc: '',
      args: [],
    );
  }

  /// `No reviews found`
  String get noReviewsFound {
    return Intl.message(
      'No reviews found',
      name: 'noReviewsFound',
      desc: '',
      args: [],
    );
  }

  /// `No reviews match the selected filter`
  String get noReviewsMatchFilter {
    return Intl.message(
      'No reviews match the selected filter',
      name: 'noReviewsMatchFilter',
      desc: '',
      args: [],
    );
  }

  /// `people found this review helpful`
  String get foundHelpful {
    return Intl.message(
      'people found this review helpful',
      name: 'foundHelpful',
      desc: '',
      args: [],
    );
  }

  /// `Select Reservation Date`
  String get selectReservationDate {
    return Intl.message(
      'Select Reservation Date',
      name: 'selectReservationDate',
      desc: '',
      args: [],
    );
  }

  /// `Review Reservation`
  String get reviewReservation {
    return Intl.message(
      'Review Reservation',
      name: 'reviewReservation',
      desc: '',
      args: [],
    );
  }

  /// `Please select both dates`
  String get pleaseSelectBothDates {
    return Intl.message(
      'Please select both dates',
      name: 'pleaseSelectBothDates',
      desc: '',
      args: [],
    );
  }

  /// `The selected period is not available, please choose another period.`
  String get selectedPeriodNotAvailable {
    return Intl.message(
      'The selected period is not available, please choose another period.',
      name: 'selectedPeriodNotAvailable',
      desc: '',
      args: [],
    );
  }

  /// `An error occurred while fetching information`
  String get errorFetchingInfo {
    return Intl.message(
      'An error occurred while fetching information',
      name: 'errorFetchingInfo',
      desc: '',
      args: [],
    );
  }

  /// `Failed to load video`
  String get failedToLoadVideo {
    return Intl.message(
      'Failed to load video',
      name: 'failedToLoadVideo',
      desc: '',
      args: [],
    );
  }

  /// `Gather Point`
  String get gatherPoint {
    return Intl.message(
      'Gather Point',
      name: 'gatherPoint',
      desc: '',
      args: [],
    );
  }

  /// `Mute Video`
  String get muteVideo {
    return Intl.message(
      'Mute Video',
      name: 'muteVideo',
      desc: '',
      args: [],
    );
  }

  /// `Unmute Video`
  String get unmuteVideo {
    return Intl.message(
      'Unmute Video',
      name: 'unmuteVideo',
      desc: '',
      args: [],
    );
  }

  /// `Play Video`
  String get playVideo {
    return Intl.message(
      'Play Video',
      name: 'playVideo',
      desc: '',
      args: [],
    );
  }

  /// `Pause Video`
  String get pauseVideo {
    return Intl.message(
      'Pause Video',
      name: 'pauseVideo',
      desc: '',
      args: [],
    );
  }

  /// `Remove from Favorites`
  String get removeFromFavorites {
    return Intl.message(
      'Remove from Favorites',
      name: 'removeFromFavorites',
      desc: '',
      args: [],
    );
  }

  /// `Comment`
  String get comment {
    return Intl.message(
      'Comment',
      name: 'comment',
      desc: '',
      args: [],
    );
  }

  /// `Share`
  String get share {
    return Intl.message(
      'Share',
      name: 'share',
      desc: '',
      args: [],
    );
  }

  /// `Comments`
  String get comments {
    return Intl.message(
      'Comments',
      name: 'comments',
      desc: '',
      args: [],
    );
  }

  /// `Write a comment...`
  String get writeComment {
    return Intl.message(
      'Write a comment...',
      name: 'writeComment',
      desc: '',
      args: [],
    );
  }

  /// `Post Comment`
  String get postComment {
    return Intl.message(
      'Post Comment',
      name: 'postComment',
      desc: '',
      args: [],
    );
  }

  /// `No comments yet`
  String get noComments {
    return Intl.message(
      'No comments yet',
      name: 'noComments',
      desc: '',
      args: [],
    );
  }

  /// `Comment posted successfully`
  String get commentPosted {
    return Intl.message(
      'Comment posted successfully',
      name: 'commentPosted',
      desc: '',
      args: [],
    );
  }

  /// `Failed to post comment`
  String get commentFailed {
    return Intl.message(
      'Failed to post comment',
      name: 'commentFailed',
      desc: '',
      args: [],
    );
  }

  /// `Delete Comment`
  String get deleteComment {
    return Intl.message(
      'Delete Comment',
      name: 'deleteComment',
      desc: '',
      args: [],
    );
  }

  /// `Edit Comment`
  String get editComment {
    return Intl.message(
      'Edit Comment',
      name: 'editComment',
      desc: '',
      args: [],
    );
  }

  /// `Reply to Comment`
  String get replyToComment {
    return Intl.message(
      'Reply to Comment',
      name: 'replyToComment',
      desc: '',
      args: [],
    );
  }

  /// `Show Comments`
  String get showComments {
    return Intl.message(
      'Show Comments',
      name: 'showComments',
      desc: '',
      args: [],
    );
  }

  /// `Hide Comments`
  String get hideComments {
    return Intl.message(
      'Hide Comments',
      name: 'hideComments',
      desc: '',
      args: [],
    );
  }

  /// `Search Reels`
  String get searchReels {
    return Intl.message(
      'Search Reels',
      name: 'searchReels',
      desc: '',
      args: [],
    );
  }

  /// `Filter Reels`
  String get filterReels {
    return Intl.message(
      'Filter Reels',
      name: 'filterReels',
      desc: '',
      args: [],
    );
  }

  /// `All Categories`
  String get allCategories {
    return Intl.message(
      'All Categories',
      name: 'allCategories',
      desc: '',
      args: [],
    );
  }

  /// `Sort By`
  String get sortBy {
    return Intl.message(
      'Sort By',
      name: 'sortBy',
      desc: '',
      args: [],
    );
  }

  /// `Newest`
  String get newest {
    return Intl.message(
      'Newest',
      name: 'newest',
      desc: '',
      args: [],
    );
  }

  /// `Oldest`
  String get oldest {
    return Intl.message(
      'Oldest',
      name: 'oldest',
      desc: '',
      args: [],
    );
  }

  /// `Most Liked`
  String get mostLiked {
    return Intl.message(
      'Most Liked',
      name: 'mostLiked',
      desc: '',
      args: [],
    );
  }

  /// `Most Commented`
  String get mostCommented {
    return Intl.message(
      'Most Commented',
      name: 'mostCommented',
      desc: '',
      args: [],
    );
  }

  /// `Apply Filter`
  String get applyFilter {
    return Intl.message(
      'Apply Filter',
      name: 'applyFilter',
      desc: '',
      args: [],
    );
  }

  /// `Clear Filter`
  String get clearFilter {
    return Intl.message(
      'Clear Filter',
      name: 'clearFilter',
      desc: '',
      args: [],
    );
  }

  /// `No results found`
  String get noResultsFound {
    return Intl.message(
      'No results found',
      name: 'noResultsFound',
      desc: '',
      args: [],
    );
  }

  /// `Additional Settings`
  String get additionalSettings {
    return Intl.message(
      'Additional Settings',
      name: 'additionalSettings',
      desc: '',
      args: [],
    );
  }

  /// `Notification Settings`
  String get notificationSettings {
    return Intl.message(
      'Notification Settings',
      name: 'notificationSettings',
      desc: '',
      args: [],
    );
  }

  /// `About App`
  String get aboutApp {
    return Intl.message(
      'About App',
      name: 'aboutApp',
      desc: '',
      args: [],
    );
  }

  /// `Filter Results`
  String get filterResults {
    return Intl.message(
      'Filter Results',
      name: 'filterResults',
      desc: '',
      args: [],
    );
  }

  /// `Price Range`
  String get priceRange {
    return Intl.message(
      'Price Range',
      name: 'priceRange',
      desc: '',
      args: [],
    );
  }

  /// `Minimum Rating`
  String get minimumRating {
    return Intl.message(
      'Minimum Rating',
      name: 'minimumRating',
      desc: '',
      args: [],
    );
  }

  /// `Reset`
  String get resetFilters {
    return Intl.message(
      'Reset',
      name: 'resetFilters',
      desc: '',
      args: [],
    );
  }

  /// `Apply`
  String get applyFilters {
    return Intl.message(
      'Apply',
      name: 'applyFilters',
      desc: '',
      args: [],
    );
  }

  /// `Try adjusting your search criteria`
  String get tryDifferentSearchCriteria {
    return Intl.message(
      'Try adjusting your search criteria',
      name: 'tryDifferentSearchCriteria',
      desc: '',
      args: [],
    );
  }

  /// `Price: Low to High`
  String get priceLowToHigh {
    return Intl.message(
      'Price: Low to High',
      name: 'priceLowToHigh',
      desc: '',
      args: [],
    );
  }

  /// `Price: High to Low`
  String get priceHighToLow {
    return Intl.message(
      'Price: High to Low',
      name: 'priceHighToLow',
      desc: '',
      args: [],
    );
  }

  /// `Rating: High to Low`
  String get ratingHighToLow {
    return Intl.message(
      'Rating: High to Low',
      name: 'ratingHighToLow',
      desc: '',
      args: [],
    );
  }

  /// `Rating: Low to High`
  String get ratingLowToHigh {
    return Intl.message(
      'Rating: Low to High',
      name: 'ratingLowToHigh',
      desc: '',
      args: [],
    );
  }

  /// `Popular`
  String get popular {
    return Intl.message(
      'Popular',
      name: 'popular',
      desc: '',
      args: [],
    );
  }

  /// `Privacy and security settings`
  String get privacyAndSecurity {
    return Intl.message(
      'Privacy and security settings',
      name: 'privacyAndSecurity',
      desc: '',
      args: [],
    );
  }

  /// `App information and version`
  String get appInformation {
    return Intl.message(
      'App information and version',
      name: 'appInformation',
      desc: '',
      args: [],
    );
  }

  /// `Push Notifications`
  String get pushNotifications {
    return Intl.message(
      'Push Notifications',
      name: 'pushNotifications',
      desc: '',
      args: [],
    );
  }

  /// `Event Notifications`
  String get eventNotifications {
    return Intl.message(
      'Event Notifications',
      name: 'eventNotifications',
      desc: '',
      args: [],
    );
  }

  /// `Message Notifications`
  String get messageNotifications {
    return Intl.message(
      'Message Notifications',
      name: 'messageNotifications',
      desc: '',
      args: [],
    );
  }

  /// `Marketing Notifications`
  String get marketingNotifications {
    return Intl.message(
      'Marketing Notifications',
      name: 'marketingNotifications',
      desc: '',
      args: [],
    );
  }

  /// `Enable or disable all notifications`
  String get enableAllNotifications {
    return Intl.message(
      'Enable or disable all notifications',
      name: 'enableAllNotifications',
      desc: '',
      args: [],
    );
  }

  /// `Notifications about new events and updates`
  String get newEventsAndUpdates {
    return Intl.message(
      'Notifications about new events and updates',
      name: 'newEventsAndUpdates',
      desc: '',
      args: [],
    );
  }

  /// `Notifications for new messages and conversations`
  String get newMessagesAndChats {
    return Intl.message(
      'Notifications for new messages and conversations',
      name: 'newMessagesAndChats',
      desc: '',
      args: [],
    );
  }

  /// `Notifications about offers and marketing news`
  String get offersAndMarketing {
    return Intl.message(
      'Notifications about offers and marketing news',
      name: 'offersAndMarketing',
      desc: '',
      args: [],
    );
  }

  /// `Test Notification`
  String get testNotification {
    return Intl.message(
      'Test Notification',
      name: 'testNotification',
      desc: '',
      args: [],
    );
  }

  /// `Send Test Notification`
  String get sendTestNotification {
    return Intl.message(
      'Send Test Notification',
      name: 'sendTestNotification',
      desc: '',
      args: [],
    );
  }

  /// `Notification Permission Required`
  String get notificationPermissionRequired {
    return Intl.message(
      'Notification Permission Required',
      name: 'notificationPermissionRequired',
      desc: '',
      args: [],
    );
  }

  /// `Please enable notifications in device settings`
  String get enableNotificationsInSettings {
    return Intl.message(
      'Please enable notifications in device settings',
      name: 'enableNotificationsInSettings',
      desc: '',
      args: [],
    );
  }

  /// `Open Settings`
  String get openSettings {
    return Intl.message(
      'Open Settings',
      name: 'openSettings',
      desc: '',
      args: [],
    );
  }

  /// `Data & Privacy`
  String get dataAndPrivacy {
    return Intl.message(
      'Data & Privacy',
      name: 'dataAndPrivacy',
      desc: '',
      args: [],
    );
  }

  /// `Data Collection`
  String get dataCollection {
    return Intl.message(
      'Data Collection',
      name: 'dataCollection',
      desc: '',
      args: [],
    );
  }

  /// `Third Party Sharing`
  String get thirdPartySharing {
    return Intl.message(
      'Third Party Sharing',
      name: 'thirdPartySharing',
      desc: '',
      args: [],
    );
  }

  /// `Data Retention`
  String get dataRetention {
    return Intl.message(
      'Data Retention',
      name: 'dataRetention',
      desc: '',
      args: [],
    );
  }

  /// `Your Rights`
  String get yourRights {
    return Intl.message(
      'Your Rights',
      name: 'yourRights',
      desc: '',
      args: [],
    );
  }

  /// `Contact Us`
  String get contactUs {
    return Intl.message(
      'Contact Us',
      name: 'contactUs',
      desc: '',
      args: [],
    );
  }

  /// `Delete Account`
  String get deleteAccount {
    return Intl.message(
      'Delete Account',
      name: 'deleteAccount',
      desc: '',
      args: [],
    );
  }

  /// `How we collect and use your data`
  String get dataCollectionDesc {
    return Intl.message(
      'How we collect and use your data',
      name: 'dataCollectionDesc',
      desc: '',
      args: [],
    );
  }

  /// `Information about data sharing with partners`
  String get thirdPartySharingDesc {
    return Intl.message(
      'Information about data sharing with partners',
      name: 'thirdPartySharingDesc',
      desc: '',
      args: [],
    );
  }

  /// `How long we keep your data`
  String get dataRetentionDesc {
    return Intl.message(
      'How long we keep your data',
      name: 'dataRetentionDesc',
      desc: '',
      args: [],
    );
  }

  /// `Your privacy rights and how to exercise them`
  String get yourRightsDesc {
    return Intl.message(
      'Your privacy rights and how to exercise them',
      name: 'yourRightsDesc',
      desc: '',
      args: [],
    );
  }

  /// `Get in touch with our support team`
  String get contactUsDesc {
    return Intl.message(
      'Get in touch with our support team',
      name: 'contactUsDesc',
      desc: '',
      args: [],
    );
  }

  /// `Permanently delete your account and data`
  String get deleteAccountDesc {
    return Intl.message(
      'Permanently delete your account and data',
      name: 'deleteAccountDesc',
      desc: '',
      args: [],
    );
  }

  /// `Continue with Google`
  String get proceedWithGoogle {
    return Intl.message(
      'Continue with Google',
      name: 'proceedWithGoogle',
      desc: '',
      args: [],
    );
  }

  /// `Continue with Apple`
  String get proceedWithApple {
    return Intl.message(
      'Continue with Apple',
      name: 'proceedWithApple',
      desc: '',
      args: [],
    );
  }

  /// `OR`
  String get or {
    return Intl.message(
      'OR',
      name: 'or',
      desc: '',
      args: [],
    );
  }

  /// `Welcome our guest... continue`
  String get welcomeGuest {
    return Intl.message(
      'Welcome our guest... continue',
      name: 'welcomeGuest',
      desc: '',
      args: [],
    );
  }

  /// `Continue as Guest`
  String get proceedAsGuest {
    return Intl.message(
      'Continue as Guest',
      name: 'proceedAsGuest',
      desc: '',
      args: [],
    );
  }

  /// `Continue with your phone number`
  String get proceedWithPhone {
    return Intl.message(
      'Continue with your phone number',
      name: 'proceedWithPhone',
      desc: '',
      args: [],
    );
  }

  /// `Verify your phone number`
  String get verifyPhoneNumber {
    return Intl.message(
      'Verify your phone number',
      name: 'verifyPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Enter verification code`
  String get enterVerificationCode {
    return Intl.message(
      'Enter verification code',
      name: 'enterVerificationCode',
      desc: '',
      args: [],
    );
  }

  /// `A 4-digit code has been sent to your phone`
  String get verificationCodeSent {
    return Intl.message(
      'A 4-digit code has been sent to your phone',
      name: 'verificationCodeSent',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get proceedLabel {
    return Intl.message(
      'Continue',
      name: 'proceedLabel',
      desc: '',
      args: [],
    );
  }

  /// `View All Bookings`
  String get viewAllBookings {
    return Intl.message(
      'View All Bookings',
      name: 'viewAllBookings',
      desc: '',
      args: [],
    );
  }

  /// `View All Reviews`
  String get viewAllReviews {
    return Intl.message(
      'View All Reviews',
      name: 'viewAllReviews',
      desc: '',
      args: [],
    );
  }

  /// `Enter your phone number`
  String get enterPhoneNumber {
    return Intl.message(
      'Enter your phone number',
      name: 'enterPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `5xxxxxxxx`
  String get phoneNumberHint {
    return Intl.message(
      '5xxxxxxxx',
      name: 'phoneNumberHint',
      desc: '',
      args: [],
    );
  }

  /// `Please enter phone number`
  String get pleaseEnterPhoneNumber {
    return Intl.message(
      'Please enter phone number',
      name: 'pleaseEnterPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Please check phone number`
  String get pleaseCheckPhoneNumber {
    return Intl.message(
      'Please check phone number',
      name: 'pleaseCheckPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Invalid phone number`
  String get invalidPhoneNumber {
    return Intl.message(
      'Invalid phone number',
      name: 'invalidPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Phone number is required`
  String get phoneNumberRequired {
    return Intl.message(
      'Phone number is required',
      name: 'phoneNumberRequired',
      desc: '',
      args: [],
    );
  }

  /// `Resend code`
  String get resendCode {
    return Intl.message(
      'Resend code',
      name: 'resendCode',
      desc: '',
      args: [],
    );
  }

  /// `Didn't receive the code?`
  String get didntReceiveCode {
    return Intl.message(
      'Didn\'t receive the code?',
      name: 'didntReceiveCode',
      desc: '',
      args: [],
    );
  }

  /// `Verification failed`
  String get verificationFailed {
    return Intl.message(
      'Verification failed',
      name: 'verificationFailed',
      desc: '',
      args: [],
    );
  }

  /// `Invalid code`
  String get invalidCode {
    return Intl.message(
      'Invalid code',
      name: 'invalidCode',
      desc: '',
      args: [],
    );
  }

  /// `Code expired`
  String get codeExpired {
    return Intl.message(
      'Code expired',
      name: 'codeExpired',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get continueButton {
    return Intl.message(
      'Continue',
      name: 'continueButton',
      desc: '',
      args: [],
    );
  }

  /// `Explore all available categories`
  String get exploreAllCategoriesSubtitle {
    return Intl.message(
      'Explore all available categories',
      name: 'exploreAllCategoriesSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Basic Information`
  String get basicInformation {
    return Intl.message(
      'Basic Information',
      name: 'basicInformation',
      desc: '',
      args: [],
    );
  }

  /// `Enter property title`
  String get enterPropertyTitle {
    return Intl.message(
      'Enter property title',
      name: 'enterPropertyTitle',
      desc: '',
      args: [],
    );
  }

  /// `Please enter property title`
  String get pleaseEnterPropertyTitle {
    return Intl.message(
      'Please enter property title',
      name: 'pleaseEnterPropertyTitle',
      desc: '',
      args: [],
    );
  }

  /// `Enter property description`
  String get enterPropertyDescription {
    return Intl.message(
      'Enter property description',
      name: 'enterPropertyDescription',
      desc: '',
      args: [],
    );
  }

  /// `Please enter property description`
  String get pleaseEnterDescription {
    return Intl.message(
      'Please enter property description',
      name: 'pleaseEnterDescription',
      desc: '',
      args: [],
    );
  }

  /// `Please enter price`
  String get pleaseEnterPrice {
    return Intl.message(
      'Please enter price',
      name: 'pleaseEnterPrice',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a valid price`
  String get pleaseEnterValidPrice {
    return Intl.message(
      'Please enter a valid price',
      name: 'pleaseEnterValidPrice',
      desc: '',
      args: [],
    );
  }

  /// `Max Guests`
  String get maxGuests {
    return Intl.message(
      'Max Guests',
      name: 'maxGuests',
      desc: '',
      args: [],
    );
  }

  /// `Please enter max guests`
  String get pleaseEnterMaxGuests {
    return Intl.message(
      'Please enter max guests',
      name: 'pleaseEnterMaxGuests',
      desc: '',
      args: [],
    );
  }

  /// `Please enter valid number`
  String get pleaseEnterValidNumber {
    return Intl.message(
      'Please enter valid number',
      name: 'pleaseEnterValidNumber',
      desc: '',
      args: [],
    );
  }

  /// `Please enter bedrooms`
  String get pleaseEnterBedrooms {
    return Intl.message(
      'Please enter bedrooms',
      name: 'pleaseEnterBedrooms',
      desc: '',
      args: [],
    );
  }

  /// `Please enter bathrooms`
  String get pleaseBathrooms {
    return Intl.message(
      'Please enter bathrooms',
      name: 'pleaseBathrooms',
      desc: '',
      args: [],
    );
  }

  /// `Category`
  String get category {
    return Intl.message(
      'Category',
      name: 'category',
      desc: '',
      args: [],
    );
  }

  /// `Please select category`
  String get pleaseSelectCategory {
    return Intl.message(
      'Please select category',
      name: 'pleaseSelectCategory',
      desc: '',
      args: [],
    );
  }

  /// `Facilities`
  String get facilities {
    return Intl.message(
      'Facilities',
      name: 'facilities',
      desc: '',
      args: [],
    );
  }

  /// `Media`
  String get media {
    return Intl.message(
      'Media',
      name: 'media',
      desc: '',
      args: [],
    );
  }

  /// `Main Image`
  String get mainImage {
    return Intl.message(
      'Main Image',
      name: 'mainImage',
      desc: '',
      args: [],
    );
  }

  /// `Video`
  String get video {
    return Intl.message(
      'Video',
      name: 'video',
      desc: '',
      args: [],
    );
  }

  /// `Gallery`
  String get gallery {
    return Intl.message(
      'Gallery',
      name: 'gallery',
      desc: '',
      args: [],
    );
  }

  /// `Add Images`
  String get addImages {
    return Intl.message(
      'Add Images',
      name: 'addImages',
      desc: '',
      args: [],
    );
  }

  /// `Booking Rules`
  String get bookingRules {
    return Intl.message(
      'Booking Rules',
      name: 'bookingRules',
      desc: '',
      args: [],
    );
  }

  /// `Enter booking rules`
  String get enterBookingRules {
    return Intl.message(
      'Enter booking rules',
      name: 'enterBookingRules',
      desc: '',
      args: [],
    );
  }

  /// `Cancellation Rules`
  String get cancellationRules {
    return Intl.message(
      'Cancellation Rules',
      name: 'cancellationRules',
      desc: '',
      args: [],
    );
  }

  /// `Enter cancellation rules`
  String get enterCancellationRules {
    return Intl.message(
      'Enter cancellation rules',
      name: 'enterCancellationRules',
      desc: '',
      args: [],
    );
  }

  /// `Review submitted successfully`
  String get reviewSubmittedSuccessfully {
    return Intl.message(
      'Review submitted successfully',
      name: 'reviewSubmittedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Submit Review`
  String get submitReview {
    return Intl.message(
      'Submit Review',
      name: 'submitReview',
      desc: '',
      args: [],
    );
  }

  /// `You need to login to make a reservation. Guest users can also make reservations with limited features.`
  String get loginRequiredForReservation {
    return Intl.message(
      'You need to login to make a reservation. Guest users can also make reservations with limited features.',
      name: 'loginRequiredForReservation',
      desc: '',
      args: [],
    );
  }

  /// `You need to login to add items to your favorites list.`
  String get loginRequiredForFavorites {
    return Intl.message(
      'You need to login to add items to your favorites list.',
      name: 'loginRequiredForFavorites',
      desc: '',
      args: [],
    );
  }

  /// `You need to login to write reviews and share your experience.`
  String get loginRequiredForReviews {
    return Intl.message(
      'You need to login to write reviews and share your experience.',
      name: 'loginRequiredForReviews',
      desc: '',
      args: [],
    );
  }

  /// `As a guest, you can browse and make reservations, but some features like favorites and reviews require an account.`
  String get guestModeInfo {
    return Intl.message(
      'As a guest, you can browse and make reservations, but some features like favorites and reviews require an account.',
      name: 'guestModeInfo',
      desc: '',
      args: [],
    );
  }

  /// `Guest Reservation`
  String get guestReservation {
    return Intl.message(
      'Guest Reservation',
      name: 'guestReservation',
      desc: '',
      args: [],
    );
  }

  /// `You are currently browsing as a guest. You can proceed with the reservation, but creating an account will give you access to more features.`
  String get guestReservationMessage {
    return Intl.message(
      'You are currently browsing as a guest. You can proceed with the reservation, but creating an account will give you access to more features.',
      name: 'guestReservationMessage',
      desc: '',
      args: [],
    );
  }

  /// `Guest Limitations:`
  String get guestLimitations {
    return Intl.message(
      'Guest Limitations:',
      name: 'guestLimitations',
      desc: '',
      args: [],
    );
  }

  /// `• Cannot save favorites\n• Cannot write reviews\n• Limited reservation history\n• No profile management`
  String get guestLimitationsDetails {
    return Intl.message(
      '• Cannot save favorites\n• Cannot write reviews\n• Limited reservation history\n• No profile management',
      name: 'guestLimitationsDetails',
      desc: '',
      args: [],
    );
  }

  /// `Login for Better Experience`
  String get loginForBetterExperience {
    return Intl.message(
      'Login for Better Experience',
      name: 'loginForBetterExperience',
      desc: '',
      args: [],
    );
  }

  /// `Continue as Guest`
  String get continueAsGuest {
    return Intl.message(
      'Continue as Guest',
      name: 'continueAsGuest',
      desc: '',
      args: [],
    );
  }

  /// `Feature Unavailable`
  String get featureUnavailable {
    return Intl.message(
      'Feature Unavailable',
      name: 'featureUnavailable',
      desc: '',
      args: [],
    );
  }

  /// `This feature requires you to login. Please create an account or login to access this feature.`
  String get featureRequiresLogin {
    return Intl.message(
      'This feature requires you to login. Please create an account or login to access this feature.',
      name: 'featureRequiresLogin',
      desc: '',
      args: [],
    );
  }

  /// `Guest`
  String get guest {
    return Intl.message(
      'Guest',
      name: 'guest',
      desc: '',
      args: [],
    );
  }

  /// `Retry Connection`
  String get retryConnection {
    return Intl.message(
      'Retry Connection',
      name: 'retryConnection',
      desc: '',
      args: [],
    );
  }

  /// `Connection Error`
  String get connectionError {
    return Intl.message(
      'Connection Error',
      name: 'connectionError',
      desc: '',
      args: [],
    );
  }

  /// `Server Error`
  String get serverError {
    return Intl.message(
      'Server Error',
      name: 'serverError',
      desc: '',
      args: [],
    );
  }

  /// `Unknown Error`
  String get unknownError {
    return Intl.message(
      'Unknown Error',
      name: 'unknownError',
      desc: '',
      args: [],
    );
  }

  /// `Loading data...`
  String get loadingData {
    return Intl.message(
      'Loading data...',
      name: 'loadingData',
      desc: '',
      args: [],
    );
  }

  /// `Refresh Data`
  String get refreshData {
    return Intl.message(
      'Refresh Data',
      name: 'refreshData',
      desc: '',
      args: [],
    );
  }

  /// `No Internet Connection`
  String get noInternetConnection {
    return Intl.message(
      'No Internet Connection',
      name: 'noInternetConnection',
      desc: '',
      args: [],
    );
  }

  /// `Please check your internet connection and try again`
  String get checkInternetConnection {
    return Intl.message(
      'Please check your internet connection and try again',
      name: 'checkInternetConnection',
      desc: '',
      args: [],
    );
  }

  /// `Failed to load data`
  String get dataLoadFailed {
    return Intl.message(
      'Failed to load data',
      name: 'dataLoadFailed',
      desc: '',
      args: [],
    );
  }

  /// `Pull to refresh`
  String get pullToRefresh {
    return Intl.message(
      'Pull to refresh',
      name: 'pullToRefresh',
      desc: '',
      args: [],
    );
  }

  /// `of`
  String get ofPreposition {
    return Intl.message(
      'of',
      name: 'ofPreposition',
      desc: '',
      args: [],
    );
  }

  /// `Tourism Permit Number *`
  String get tourismPermitNumber {
    return Intl.message(
      'Tourism Permit Number *',
      name: 'tourismPermitNumber',
      desc: '',
      args: [],
    );
  }

  /// `Error loading data`
  String get dataLoadError {
    return Intl.message(
      'Error loading data',
      name: 'dataLoadError',
      desc: '',
      args: [],
    );
  }

  /// `No data available`
  String get noDataAvailable {
    return Intl.message(
      'No data available',
      name: 'noDataAvailable',
      desc: '',
      args: [],
    );
  }

  /// `review`
  String get reviewsCount {
    return Intl.message(
      'review',
      name: 'reviewsCount',
      desc: '',
      args: [],
    );
  }

  /// `SR/night`
  String get sarPerNight {
    return Intl.message(
      'SR/night',
      name: 'sarPerNight',
      desc: '',
      args: [],
    );
  }

  /// `Free WiFi`
  String get freeWifiArabic {
    return Intl.message(
      'Free WiFi',
      name: 'freeWifiArabic',
      desc: '',
      args: [],
    );
  }

  /// `year`
  String get year {
    return Intl.message(
      'year',
      name: 'year',
      desc: '',
      args: [],
    );
  }

  /// `years`
  String get years {
    return Intl.message(
      'years',
      name: 'years',
      desc: '',
      args: [],
    );
  }

  /// `hosting`
  String get inHosting {
    return Intl.message(
      'hosting',
      name: 'inHosting',
      desc: '',
      args: [],
    );
  }

  /// `New host`
  String get newHost {
    return Intl.message(
      'New host',
      name: 'newHost',
      desc: '',
      args: [],
    );
  }

  /// `No description available.`
  String get noDescriptionAvailable {
    return Intl.message(
      'No description available.',
      name: 'noDescriptionAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Guest review`
  String get guestRating {
    return Intl.message(
      'Guest review',
      name: 'guestRating',
      desc: '',
      args: [],
    );
  }

  /// `March 2024`
  String get march2024 {
    return Intl.message(
      'March 2024',
      name: 'march2024',
      desc: '',
      args: [],
    );
  }

  /// `Great place to stay! Clean and comfortable and exactly as described. The host was very responsive and helpful.`
  String get sampleReviewText {
    return Intl.message(
      'Great place to stay! Clean and comfortable and exactly as described. The host was very responsive and helpful.',
      name: 'sampleReviewText',
      desc: '',
      args: [],
    );
  }

  /// `Show all`
  String get showAllReviews {
    return Intl.message(
      'Show all',
      name: 'showAllReviews',
      desc: '',
      args: [],
    );
  }

  /// `SR`
  String get sar {
    return Intl.message(
      'SR',
      name: 'sar',
      desc: '',
      args: [],
    );
  }

  /// `night`
  String get night {
    return Intl.message(
      'night',
      name: 'night',
      desc: '',
      args: [],
    );
  }

  /// `SR`
  String get currencySymbol {
    return Intl.message(
      'SR',
      name: 'currencySymbol',
      desc: 'Currency symbol for Saudi Riyal',
      args: [],
    );
  }

  /// `SAR`
  String get currencyCode {
    return Intl.message(
      'SAR',
      name: 'currencyCode',
      desc: 'Currency code for Saudi Riyal',
      args: [],
    );
  }

  /// `{price} SR`
  String priceWithCurrency(String price) {
    return Intl.message(
      '$price SR',
      name: 'priceWithCurrency',
      desc: 'Price format with currency symbol',
      args: [price],
    );
  }

  /// `Minimum withdrawal: SR 50`
  String get minimumWithdrawAmount {
    return Intl.message(
      'Minimum withdrawal: SR 50',
      name: 'minimumWithdrawAmount',
      desc: 'Minimum withdrawal amount with currency',
      args: [],
    );
  }

  /// `Smart Entry`
  String get smartEntry {
    return Intl.message(
      'Smart Entry',
      name: 'smartEntry',
      desc: 'Label for smart entry facility in place quick stats.',
      args: [],
    );
  }

  /// `Knowledge`
  String get knowledge {
    return Intl.message(
      'Knowledge',
      name: 'knowledge',
      desc: '',
      args: [],
    );
  }

  /// `Previous Trips`
  String get previousTrips {
    return Intl.message(
      'Previous Trips',
      name: 'previousTrips',
      desc: '',
      args: [],
    );
  }

  /// `Join as Host`
  String get joinAsHost {
    return Intl.message(
      'Join as Host',
      name: 'joinAsHost',
      desc: '',
      args: [],
    );
  }

  /// `It's easy to start hosting and earn extra income`
  String get joinAsHostSubtitle {
    return Intl.message(
      'It\'s easy to start hosting and earn extra income',
      name: 'joinAsHostSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Account Settings`
  String get accountSettings {
    return Intl.message(
      'Account Settings',
      name: 'accountSettings',
      desc: '',
      args: [],
    );
  }

  /// `Request Help`
  String get requestHelp {
    return Intl.message(
      'Request Help',
      name: 'requestHelp',
      desc: '',
      args: [],
    );
  }

  /// `View Profile`
  String get viewProfile {
    return Intl.message(
      'View Profile',
      name: 'viewProfile',
      desc: '',
      args: [],
    );
  }

  /// `Refer Host`
  String get referHost {
    return Intl.message(
      'Refer Host',
      name: 'referHost',
      desc: '',
      args: [],
    );
  }

  /// `Legal`
  String get legal {
    return Intl.message(
      'Legal',
      name: 'legal',
      desc: '',
      args: [],
    );
  }

  /// `Previous Trip`
  String get previousTrip {
    return Intl.message(
      'Previous Trip',
      name: 'previousTrip',
      desc: '',
      args: [],
    );
  }

  /// `Years on Airbnb`
  String get yearsOnAirbnb {
    return Intl.message(
      'Years on Airbnb',
      name: 'yearsOnAirbnb',
      desc: '',
      args: [],
    );
  }

  /// `Remember that the policy set by the host suits your circumstances. In rare cases, you may be eligible for a partial or full refund according to the site's policy.`
  String get cancellationPolicyNote {
    return Intl.message(
      'Remember that the policy set by the host suits your circumstances. In rare cases, you may be eligible for a partial or full refund according to the site\'s policy.',
      name: 'cancellationPolicyNote',
      desc: '',
      args: [],
    );
  }

  /// `Select Cancellation Policy`
  String get selectCancellationPolicy {
    return Intl.message(
      'Select Cancellation Policy',
      name: 'selectCancellationPolicy',
      desc: '',
      args: [],
    );
  }

  /// `Short-term bookings (≤28 days)`
  String get shortTermBookings {
    return Intl.message(
      'Short-term bookings (≤28 days)',
      name: 'shortTermBookings',
      desc: '',
      args: [],
    );
  }

  /// `Long-term bookings (>28 days)`
  String get longTermBookings {
    return Intl.message(
      'Long-term bookings (>28 days)',
      name: 'longTermBookings',
      desc: '',
      args: [],
    );
  }

  /// `Flexible`
  String get flexiblePolicy {
    return Intl.message(
      'Flexible',
      name: 'flexiblePolicy',
      desc: '',
      args: [],
    );
  }

  /// `Moderate`
  String get moderatePolicy {
    return Intl.message(
      'Moderate',
      name: 'moderatePolicy',
      desc: '',
      args: [],
    );
  }

  /// `Strict`
  String get strictPolicy {
    return Intl.message(
      'Strict',
      name: 'strictPolicy',
      desc: '',
      args: [],
    );
  }

  /// `Policy Description`
  String get policyDescription {
    return Intl.message(
      'Policy Description',
      name: 'policyDescription',
      desc: '',
      args: [],
    );
  }

  /// `Please select property type`
  String get pleaseSelectPropertyType {
    return Intl.message(
      'Please select property type',
      name: 'pleaseSelectPropertyType',
      desc: '',
      args: [],
    );
  }

  /// `Please select cancellation policy`
  String get pleaseSelectCancellationPolicy {
    return Intl.message(
      'Please select cancellation policy',
      name: 'pleaseSelectCancellationPolicy',
      desc: '',
      args: [],
    );
  }

  /// `Select Location`
  String get selectLocation {
    return Intl.message(
      'Select Location',
      name: 'selectLocation',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Location`
  String get confirmLocation {
    return Intl.message(
      'Confirm Location',
      name: 'confirmLocation',
      desc: '',
      args: [],
    );
  }

  /// `Confirm`
  String get confirm {
    return Intl.message(
      'Confirm',
      name: 'confirm',
      desc: '',
      args: [],
    );
  }

  /// `Property Preview`
  String get propertyPreview {
    return Intl.message(
      'Property Preview',
      name: 'propertyPreview',
      desc: '',
      args: [],
    );
  }

  /// `Publish Property`
  String get publishProperty {
    return Intl.message(
      'Publish Property',
      name: 'publishProperty',
      desc: '',
      args: [],
    );
  }

  /// `Policies`
  String get policies {
    return Intl.message(
      'Policies',
      name: 'policies',
      desc: '',
      args: [],
    );
  }

  /// `List View`
  String get listView {
    return Intl.message(
      'List View',
      name: 'listView',
      desc: '',
      args: [],
    );
  }

  /// `Grid View`
  String get gridView {
    return Intl.message(
      'Grid View',
      name: 'gridView',
      desc: '',
      args: [],
    );
  }

  /// `Cancel Selection`
  String get cancelSelection {
    return Intl.message(
      'Cancel Selection',
      name: 'cancelSelection',
      desc: '',
      args: [],
    );
  }

  /// `Select Multiple`
  String get selectMultiple {
    return Intl.message(
      'Select Multiple',
      name: 'selectMultiple',
      desc: '',
      args: [],
    );
  }

  /// `Add New Listing`
  String get addNewListing {
    return Intl.message(
      'Add New Listing',
      name: 'addNewListing',
      desc: '',
      args: [],
    );
  }

  /// `Dashboard Overview`
  String get dashboardOverview {
    return Intl.message(
      'Dashboard Overview',
      name: 'dashboardOverview',
      desc: '',
      args: [],
    );
  }

  /// `Total Revenue`
  String get totalRevenue {
    return Intl.message(
      'Total Revenue',
      name: 'totalRevenue',
      desc: '',
      args: [],
    );
  }

  /// `Average Rating`
  String get averageRating {
    return Intl.message(
      'Average Rating',
      name: 'averageRating',
      desc: '',
      args: [],
    );
  }

  /// `Active Listings`
  String get activeListings {
    return Intl.message(
      'Active Listings',
      name: 'activeListings',
      desc: '',
      args: [],
    );
  }

  /// `Occupancy Rate`
  String get occupancyRate {
    return Intl.message(
      'Occupancy Rate',
      name: 'occupancyRate',
      desc: '',
      args: [],
    );
  }

  /// `Conversion Rate`
  String get conversionRate {
    return Intl.message(
      'Conversion Rate',
      name: 'conversionRate',
      desc: '',
      args: [],
    );
  }

  /// `All Listings`
  String get allListings {
    return Intl.message(
      'All Listings',
      name: 'allListings',
      desc: '',
      args: [],
    );
  }

  /// `Inactive Listings`
  String get inactiveListings {
    return Intl.message(
      'Inactive Listings',
      name: 'inactiveListings',
      desc: '',
      args: [],
    );
  }

  /// `Drafts`
  String get drafts {
    return Intl.message(
      'Drafts',
      name: 'drafts',
      desc: '',
      args: [],
    );
  }

  /// `Pending Review`
  String get pendingReview {
    return Intl.message(
      'Pending Review',
      name: 'pendingReview',
      desc: '',
      args: [],
    );
  }

  /// `Top Performing`
  String get topPerforming {
    return Intl.message(
      'Top Performing',
      name: 'topPerforming',
      desc: '',
      args: [],
    );
  }

  /// `Needs Attention`
  String get needsAttention {
    return Intl.message(
      'Needs Attention',
      name: 'needsAttention',
      desc: '',
      args: [],
    );
  }

  /// `Search listings...`
  String get searchListings {
    return Intl.message(
      'Search listings...',
      name: 'searchListings',
      desc: '',
      args: [],
    );
  }

  /// `Clear Filters`
  String get clearFilters {
    return Intl.message(
      'Clear Filters',
      name: 'clearFilters',
      desc: '',
      args: [],
    );
  }

  /// `No Listings Yet`
  String get noListingsYet {
    return Intl.message(
      'No Listings Yet',
      name: 'noListingsYet',
      desc: '',
      args: [],
    );
  }

  /// `Start your hosting journey by creating your first property listing. Share your space with travelers and start earning!`
  String get noListingsDescription {
    return Intl.message(
      'Start your hosting journey by creating your first property listing. Share your space with travelers and start earning!',
      name: 'noListingsDescription',
      desc: '',
      args: [],
    );
  }

  /// `Create Your First Listing`
  String get createFirstListing {
    return Intl.message(
      'Create Your First Listing',
      name: 'createFirstListing',
      desc: '',
      args: [],
    );
  }

  /// `Hosting Tips`
  String get hostingTips {
    return Intl.message(
      'Hosting Tips',
      name: 'hostingTips',
      desc: '',
      args: [],
    );
  }

  /// `Add high-quality photos to attract more guests`
  String get tip1 {
    return Intl.message(
      'Add high-quality photos to attract more guests',
      name: 'tip1',
      desc: '',
      args: [],
    );
  }

  /// `Write a detailed description of your property`
  String get tip2 {
    return Intl.message(
      'Write a detailed description of your property',
      name: 'tip2',
      desc: '',
      args: [],
    );
  }

  /// `Set competitive pricing for your area`
  String get tip3 {
    return Intl.message(
      'Set competitive pricing for your area',
      name: 'tip3',
      desc: '',
      args: [],
    );
  }

  /// `Need help?`
  String get needHelp {
    return Intl.message(
      'Need help?',
      name: 'needHelp',
      desc: '',
      args: [],
    );
  }

  /// `Contact Support`
  String get contactSupport {
    return Intl.message(
      'Contact Support',
      name: 'contactSupport',
      desc: '',
      args: [],
    );
  }

  /// `Error Loading Listings`
  String get errorLoadingListings {
    return Intl.message(
      'Error Loading Listings',
      name: 'errorLoadingListings',
      desc: '',
      args: [],
    );
  }

  /// `Try Again`
  String get tryAgain {
    return Intl.message(
      'Try Again',
      name: 'tryAgain',
      desc: '',
      args: [],
    );
  }

  /// `If the error persists, please contact support`
  String get errorPersistsContact {
    return Intl.message(
      'If the error persists, please contact support',
      name: 'errorPersistsContact',
      desc: '',
      args: [],
    );
  }

  /// `Listing Status`
  String get listingStatus {
    return Intl.message(
      'Listing Status',
      name: 'listingStatus',
      desc: '',
      args: [],
    );
  }

  /// `Publish Listing`
  String get publishListing {
    return Intl.message(
      'Publish Listing',
      name: 'publishListing',
      desc: '',
      args: [],
    );
  }

  /// `Edit Listing`
  String get editListing {
    return Intl.message(
      'Edit Listing',
      name: 'editListing',
      desc: '',
      args: [],
    );
  }

  /// `Deactivate`
  String get deactivate {
    return Intl.message(
      'Deactivate',
      name: 'deactivate',
      desc: '',
      args: [],
    );
  }

  /// `Activate`
  String get activate {
    return Intl.message(
      'Activate',
      name: 'activate',
      desc: '',
      args: [],
    );
  }

  /// `Edit While Pending`
  String get editWhilePending {
    return Intl.message(
      'Edit While Pending',
      name: 'editWhilePending',
      desc: '',
      args: [],
    );
  }

  /// `Rejection Reason`
  String get rejectionReason {
    return Intl.message(
      'Rejection Reason',
      name: 'rejectionReason',
      desc: '',
      args: [],
    );
  }

  /// `Pending Reservations`
  String get pendingReservations {
    return Intl.message(
      'Pending Reservations',
      name: 'pendingReservations',
      desc: '',
      args: [],
    );
  }

  /// `View Reservations`
  String get viewReservations {
    return Intl.message(
      'View Reservations',
      name: 'viewReservations',
      desc: '',
      args: [],
    );
  }

  /// `Your listing is live and visible to guests`
  String get activeStatusDescription {
    return Intl.message(
      'Your listing is live and visible to guests',
      name: 'activeStatusDescription',
      desc: '',
      args: [],
    );
  }

  /// `Your listing is hidden from guests`
  String get inactiveStatusDescription {
    return Intl.message(
      'Your listing is hidden from guests',
      name: 'inactiveStatusDescription',
      desc: '',
      args: [],
    );
  }

  /// `Your listing is saved but not published yet`
  String get draftStatusDescription {
    return Intl.message(
      'Your listing is saved but not published yet',
      name: 'draftStatusDescription',
      desc: '',
      args: [],
    );
  }

  /// `Your listing is under review by our team`
  String get pendingStatusDescription {
    return Intl.message(
      'Your listing is under review by our team',
      name: 'pendingStatusDescription',
      desc: '',
      args: [],
    );
  }

  /// `Your listing has been suspended`
  String get suspendedStatusDescription {
    return Intl.message(
      'Your listing has been suspended',
      name: 'suspendedStatusDescription',
      desc: '',
      args: [],
    );
  }

  /// `Unknown status`
  String get unknownStatusDescription {
    return Intl.message(
      'Unknown status',
      name: 'unknownStatusDescription',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to publish this listing?`
  String get publishListingConfirmation {
    return Intl.message(
      'Are you sure you want to publish this listing?',
      name: 'publishListingConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to deactivate this listing?`
  String get deactivateListingConfirmation {
    return Intl.message(
      'Are you sure you want to deactivate this listing?',
      name: 'deactivateListingConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `Deactivate Listing`
  String get deactivateListing {
    return Intl.message(
      'Deactivate Listing',
      name: 'deactivateListing',
      desc: '',
      args: [],
    );
  }

  /// `Change Listing Status`
  String get changeListingStatus {
    return Intl.message(
      'Change Listing Status',
      name: 'changeListingStatus',
      desc: '',
      args: [],
    );
  }

  /// `Current Status`
  String get currentStatus {
    return Intl.message(
      'Current Status',
      name: 'currentStatus',
      desc: '',
      args: [],
    );
  }

  /// `Select New Status`
  String get selectNewStatus {
    return Intl.message(
      'Select New Status',
      name: 'selectNewStatus',
      desc: '',
      args: [],
    );
  }

  /// `Change Reason`
  String get changeReason {
    return Intl.message(
      'Change Reason',
      name: 'changeReason',
      desc: '',
      args: [],
    );
  }

  /// `Enter reason for status change...`
  String get enterChangeReason {
    return Intl.message(
      'Enter reason for status change...',
      name: 'enterChangeReason',
      desc: '',
      args: [],
    );
  }

  /// `Change Status`
  String get changeStatus {
    return Intl.message(
      'Change Status',
      name: 'changeStatus',
      desc: '',
      args: [],
    );
  }

  /// `Status changed successfully`
  String get statusChangedSuccessfully {
    return Intl.message(
      'Status changed successfully',
      name: 'statusChangedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Error changing status`
  String get statusChangeError {
    return Intl.message(
      'Error changing status',
      name: 'statusChangeError',
      desc: '',
      args: [],
    );
  }

  /// `Bulk Actions`
  String get bulkActions {
    return Intl.message(
      'Bulk Actions',
      name: 'bulkActions',
      desc: '',
      args: [],
    );
  }

  /// `listings selected`
  String get listingsSelected {
    return Intl.message(
      'listings selected',
      name: 'listingsSelected',
      desc: '',
      args: [],
    );
  }

  /// `Current Status Breakdown`
  String get currentStatusBreakdown {
    return Intl.message(
      'Current Status Breakdown',
      name: 'currentStatusBreakdown',
      desc: '',
      args: [],
    );
  }

  /// `Select Action`
  String get selectAction {
    return Intl.message(
      'Select Action',
      name: 'selectAction',
      desc: '',
      args: [],
    );
  }

  /// `Apply Action`
  String get applyAction {
    return Intl.message(
      'Apply Action',
      name: 'applyAction',
      desc: '',
      args: [],
    );
  }

  /// `Activate All`
  String get activateAll {
    return Intl.message(
      'Activate All',
      name: 'activateAll',
      desc: '',
      args: [],
    );
  }

  /// `Deactivate All`
  String get deactivateAll {
    return Intl.message(
      'Deactivate All',
      name: 'deactivateAll',
      desc: '',
      args: [],
    );
  }

  /// `Convert to Draft`
  String get convertToDraft {
    return Intl.message(
      'Convert to Draft',
      name: 'convertToDraft',
      desc: '',
      args: [],
    );
  }

  /// `Delete All`
  String get deleteAll {
    return Intl.message(
      'Delete All',
      name: 'deleteAll',
      desc: '',
      args: [],
    );
  }

  /// `Make all selected listings visible to guests`
  String get activateAllDescription {
    return Intl.message(
      'Make all selected listings visible to guests',
      name: 'activateAllDescription',
      desc: '',
      args: [],
    );
  }

  /// `Hide all selected listings from guests`
  String get deactivateAllDescription {
    return Intl.message(
      'Hide all selected listings from guests',
      name: 'deactivateAllDescription',
      desc: '',
      args: [],
    );
  }

  /// `Convert all selected listings to draft status`
  String get convertToDraftDescription {
    return Intl.message(
      'Convert all selected listings to draft status',
      name: 'convertToDraftDescription',
      desc: '',
      args: [],
    );
  }

  /// `Permanently delete all selected listings`
  String get deleteAllDescription {
    return Intl.message(
      'Permanently delete all selected listings',
      name: 'deleteAllDescription',
      desc: '',
      args: [],
    );
  }

  /// `Bulk action completed successfully`
  String get bulkActionCompleted {
    return Intl.message(
      'Bulk action completed successfully',
      name: 'bulkActionCompleted',
      desc: '',
      args: [],
    );
  }

  /// `Error performing bulk action`
  String get bulkActionError {
    return Intl.message(
      'Error performing bulk action',
      name: 'bulkActionError',
      desc: '',
      args: [],
    );
  }

  /// `Analytics Overview`
  String get analyticsOverview {
    return Intl.message(
      'Analytics Overview',
      name: 'analyticsOverview',
      desc: '',
      args: [],
    );
  }

  /// `Last 7 Days`
  String get last7Days {
    return Intl.message(
      'Last 7 Days',
      name: 'last7Days',
      desc: '',
      args: [],
    );
  }

  /// `Last 30 Days`
  String get last30Days {
    return Intl.message(
      'Last 30 Days',
      name: 'last30Days',
      desc: '',
      args: [],
    );
  }

  /// `Last 90 Days`
  String get last90Days {
    return Intl.message(
      'Last 90 Days',
      name: 'last90Days',
      desc: '',
      args: [],
    );
  }

  /// `Last Year`
  String get lastYear {
    return Intl.message(
      'Last Year',
      name: 'lastYear',
      desc: '',
      args: [],
    );
  }

  /// `Export Data`
  String get exportData {
    return Intl.message(
      'Export Data',
      name: 'exportData',
      desc: '',
      args: [],
    );
  }

  /// `Performance Grade`
  String get performanceGrade {
    return Intl.message(
      'Performance Grade',
      name: 'performanceGrade',
      desc: '',
      args: [],
    );
  }

  /// `Overview`
  String get overview {
    return Intl.message(
      'Overview',
      name: 'overview',
      desc: '',
      args: [],
    );
  }

  /// `Charts`
  String get charts {
    return Intl.message(
      'Charts',
      name: 'charts',
      desc: '',
      args: [],
    );
  }

  /// `Insights`
  String get insights {
    return Intl.message(
      'Insights',
      name: 'insights',
      desc: '',
      args: [],
    );
  }

  /// `Details`
  String get details {
    return Intl.message(
      'Details',
      name: 'details',
      desc: '',
      args: [],
    );
  }

  /// `Response Time`
  String get responseTime {
    return Intl.message(
      'Response Time',
      name: 'responseTime',
      desc: '',
      args: [],
    );
  }

  /// `Response Rate`
  String get responseRate {
    return Intl.message(
      'Response Rate',
      name: 'responseRate',
      desc: '',
      args: [],
    );
  }

  /// `Views Trend`
  String get viewsTrend {
    return Intl.message(
      'Views Trend',
      name: 'viewsTrend',
      desc: '',
      args: [],
    );
  }

  /// `Daily Views`
  String get dailyViews {
    return Intl.message(
      'Daily Views',
      name: 'dailyViews',
      desc: '',
      args: [],
    );
  }

  /// `Daily Bookings`
  String get dailyBookings {
    return Intl.message(
      'Daily Bookings',
      name: 'dailyBookings',
      desc: '',
      args: [],
    );
  }

  /// `Daily Revenue`
  String get dailyRevenue {
    return Intl.message(
      'Daily Revenue',
      name: 'dailyRevenue',
      desc: '',
      args: [],
    );
  }

  /// `Booking Metrics`
  String get bookingMetrics {
    return Intl.message(
      'Booking Metrics',
      name: 'bookingMetrics',
      desc: '',
      args: [],
    );
  }

  /// `Cancelled Bookings`
  String get cancelledBookings {
    return Intl.message(
      'Cancelled Bookings',
      name: 'cancelledBookings',
      desc: '',
      args: [],
    );
  }

  /// `Cancellation Rate`
  String get cancellationRate {
    return Intl.message(
      'Cancellation Rate',
      name: 'cancellationRate',
      desc: '',
      args: [],
    );
  }

  /// `Revenue Metrics`
  String get revenueMetrics {
    return Intl.message(
      'Revenue Metrics',
      name: 'revenueMetrics',
      desc: '',
      args: [],
    );
  }

  /// `Net Revenue`
  String get netRevenue {
    return Intl.message(
      'Net Revenue',
      name: 'netRevenue',
      desc: '',
      args: [],
    );
  }

  /// `Average Daily Rate`
  String get averageDailyRate {
    return Intl.message(
      'Average Daily Rate',
      name: 'averageDailyRate',
      desc: '',
      args: [],
    );
  }

  /// `Engagement Metrics`
  String get engagementMetrics {
    return Intl.message(
      'Engagement Metrics',
      name: 'engagementMetrics',
      desc: '',
      args: [],
    );
  }

  /// `Unique Views`
  String get uniqueViews {
    return Intl.message(
      'Unique Views',
      name: 'uniqueViews',
      desc: '',
      args: [],
    );
  }

  /// `Favorites`
  String get favoriteCount {
    return Intl.message(
      'Favorites',
      name: 'favoriteCount',
      desc: '',
      args: [],
    );
  }

  /// `Shares`
  String get shareCount {
    return Intl.message(
      'Shares',
      name: 'shareCount',
      desc: '',
      args: [],
    );
  }

  /// `Analytics`
  String get analytics {
    return Intl.message(
      'Analytics',
      name: 'analytics',
      desc: '',
      args: [],
    );
  }

  /// `Advanced Bulk Actions`
  String get advancedBulkActions {
    return Intl.message(
      'Advanced Bulk Actions',
      name: 'advancedBulkActions',
      desc: '',
      args: [],
    );
  }

  /// `Total Value`
  String get totalValue {
    return Intl.message(
      'Total Value',
      name: 'totalValue',
      desc: '',
      args: [],
    );
  }

  /// `Select Action Category`
  String get selectActionCategory {
    return Intl.message(
      'Select Action Category',
      name: 'selectActionCategory',
      desc: '',
      args: [],
    );
  }

  /// `Status Actions`
  String get statusActions {
    return Intl.message(
      'Status Actions',
      name: 'statusActions',
      desc: '',
      args: [],
    );
  }

  /// `Pricing Actions`
  String get pricingActions {
    return Intl.message(
      'Pricing Actions',
      name: 'pricingActions',
      desc: '',
      args: [],
    );
  }

  /// `Management Actions`
  String get managementActions {
    return Intl.message(
      'Management Actions',
      name: 'managementActions',
      desc: '',
      args: [],
    );
  }

  /// `Execute Action`
  String get executeAction {
    return Intl.message(
      'Execute Action',
      name: 'executeAction',
      desc: '',
      args: [],
    );
  }

  /// `Action Parameters`
  String get actionParameters {
    return Intl.message(
      'Action Parameters',
      name: 'actionParameters',
      desc: '',
      args: [],
    );
  }

  /// `Percentage`
  String get percentage {
    return Intl.message(
      'Percentage',
      name: 'percentage',
      desc: '',
      args: [],
    );
  }

  /// `New Price`
  String get newPrice {
    return Intl.message(
      'New Price',
      name: 'newPrice',
      desc: '',
      args: [],
    );
  }

  /// `Discount Percentage`
  String get discountPercentage {
    return Intl.message(
      'Discount Percentage',
      name: 'discountPercentage',
      desc: '',
      args: [],
    );
  }

  /// `Discount Duration`
  String get discountDuration {
    return Intl.message(
      'Discount Duration',
      name: 'discountDuration',
      desc: '',
      args: [],
    );
  }

  /// `Publish All`
  String get publishAll {
    return Intl.message(
      'Publish All',
      name: 'publishAll',
      desc: '',
      args: [],
    );
  }

  /// `Increase Prices`
  String get increasePrices {
    return Intl.message(
      'Increase Prices',
      name: 'increasePrices',
      desc: '',
      args: [],
    );
  }

  /// `Decrease Prices`
  String get decreasePrices {
    return Intl.message(
      'Decrease Prices',
      name: 'decreasePrices',
      desc: '',
      args: [],
    );
  }

  /// `Set Prices`
  String get setPrices {
    return Intl.message(
      'Set Prices',
      name: 'setPrices',
      desc: '',
      args: [],
    );
  }

  /// `Apply Discount`
  String get applyDiscount {
    return Intl.message(
      'Apply Discount',
      name: 'applyDiscount',
      desc: '',
      args: [],
    );
  }

  /// `Duplicate All`
  String get duplicateAll {
    return Intl.message(
      'Duplicate All',
      name: 'duplicateAll',
      desc: '',
      args: [],
    );
  }

  /// `Export All`
  String get exportAll {
    return Intl.message(
      'Export All',
      name: 'exportAll',
      desc: '',
      args: [],
    );
  }

  /// `Archive All`
  String get archiveAll {
    return Intl.message(
      'Archive All',
      name: 'archiveAll',
      desc: '',
      args: [],
    );
  }

  /// `Publish all selected listings`
  String get publishAllDescription {
    return Intl.message(
      'Publish all selected listings',
      name: 'publishAllDescription',
      desc: '',
      args: [],
    );
  }

  /// `Increase prices by percentage`
  String get increasePricesDescription {
    return Intl.message(
      'Increase prices by percentage',
      name: 'increasePricesDescription',
      desc: '',
      args: [],
    );
  }

  /// `Decrease prices by percentage`
  String get decreasePricesDescription {
    return Intl.message(
      'Decrease prices by percentage',
      name: 'decreasePricesDescription',
      desc: '',
      args: [],
    );
  }

  /// `Set fixed price for all listings`
  String get setPricesDescription {
    return Intl.message(
      'Set fixed price for all listings',
      name: 'setPricesDescription',
      desc: '',
      args: [],
    );
  }

  /// `Apply temporary discount`
  String get applyDiscountDescription {
    return Intl.message(
      'Apply temporary discount',
      name: 'applyDiscountDescription',
      desc: '',
      args: [],
    );
  }

  /// `Create copies of selected listings`
  String get duplicateAllDescription {
    return Intl.message(
      'Create copies of selected listings',
      name: 'duplicateAllDescription',
      desc: '',
      args: [],
    );
  }

  /// `Export listing data`
  String get exportAllDescription {
    return Intl.message(
      'Export listing data',
      name: 'exportAllDescription',
      desc: '',
      args: [],
    );
  }

  /// `Archive selected listings`
  String get archiveAllDescription {
    return Intl.message(
      'Archive selected listings',
      name: 'archiveAllDescription',
      desc: '',
      args: [],
    );
  }

  /// `items selected`
  String get itemsSelected {
    return Intl.message(
      'items selected',
      name: 'itemsSelected',
      desc: '',
      args: [],
    );
  }

  /// `Clear Selection`
  String get clearSelection {
    return Intl.message(
      'Clear Selection',
      name: 'clearSelection',
      desc: '',
      args: [],
    );
  }

  /// `Select All`
  String get selectAll {
    return Intl.message(
      'Select All',
      name: 'selectAll',
      desc: '',
      args: [],
    );
  }

  /// `More Actions`
  String get moreActions {
    return Intl.message(
      'More Actions',
      name: 'moreActions',
      desc: '',
      args: [],
    );
  }

  /// `Activate Selected`
  String get activateSelected {
    return Intl.message(
      'Activate Selected',
      name: 'activateSelected',
      desc: '',
      args: [],
    );
  }

  /// `Deactivate Selected`
  String get deactivateSelected {
    return Intl.message(
      'Deactivate Selected',
      name: 'deactivateSelected',
      desc: '',
      args: [],
    );
  }

  /// `Delete Selected`
  String get deleteSelected {
    return Intl.message(
      'Delete Selected',
      name: 'deleteSelected',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to activate the selected listings?`
  String get activateSelectedConfirmation {
    return Intl.message(
      'Are you sure you want to activate the selected listings?',
      name: 'activateSelectedConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to deactivate the selected listings?`
  String get deactivateSelectedConfirmation {
    return Intl.message(
      'Are you sure you want to deactivate the selected listings?',
      name: 'deactivateSelectedConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete the selected listings? This action cannot be undone.`
  String get deleteSelectedConfirmation {
    return Intl.message(
      'Are you sure you want to delete the selected listings? This action cannot be undone.',
      name: 'deleteSelectedConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `listings will be affected`
  String get listingsWillBeAffected {
    return Intl.message(
      'listings will be affected',
      name: 'listingsWillBeAffected',
      desc: '',
      args: [],
    );
  }

  /// `Loading property data...`
  String get loadingPropertyData {
    return Intl.message(
      'Loading property data...',
      name: 'loadingPropertyData',
      desc: '',
      args: [],
    );
  }

  /// `Error Loading Property`
  String get errorLoadingProperty {
    return Intl.message(
      'Error Loading Property',
      name: 'errorLoadingProperty',
      desc: '',
      args: [],
    );
  }

  /// `Go Back`
  String get goBack {
    return Intl.message(
      'Go Back',
      name: 'goBack',
      desc: '',
      args: [],
    );
  }

  /// `Property Not Found`
  String get propertyNotFound {
    return Intl.message(
      'Property Not Found',
      name: 'propertyNotFound',
      desc: '',
      args: [],
    );
  }

  /// `The property you're looking for doesn't exist or has been removed.`
  String get propertyNotFoundDescription {
    return Intl.message(
      'The property you\'re looking for doesn\'t exist or has been removed.',
      name: 'propertyNotFoundDescription',
      desc: '',
      args: [],
    );
  }

  /// `Create New Property`
  String get createNewProperty {
    return Intl.message(
      'Create New Property',
      name: 'createNewProperty',
      desc: '',
      args: [],
    );
  }

  /// `Property Images`
  String get propertyImages {
    return Intl.message(
      'Property Images',
      name: 'propertyImages',
      desc: '',
      args: [],
    );
  }

  /// `Please enter property title`
  String get pleaseEnterTitle {
    return Intl.message(
      'Please enter property title',
      name: 'pleaseEnterTitle',
      desc: '',
      args: [],
    );
  }

  /// `Address`
  String get address {
    return Intl.message(
      'Address',
      name: 'address',
      desc: '',
      args: [],
    );
  }

  /// `Save as Draft`
  String get saveAsDraft {
    return Intl.message(
      'Save as Draft',
      name: 'saveAsDraft',
      desc: '',
      args: [],
    );
  }

  /// `Update Property`
  String get updateProperty {
    return Intl.message(
      'Update Property',
      name: 'updateProperty',
      desc: '',
      args: [],
    );
  }

  /// `Property updated successfully`
  String get propertyUpdatedSuccessfully {
    return Intl.message(
      'Property updated successfully',
      name: 'propertyUpdatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Property title is required`
  String get propertyTitleRequired {
    return Intl.message(
      'Property title is required',
      name: 'propertyTitleRequired',
      desc: '',
      args: [],
    );
  }

  /// `Property title must be at least 3 characters`
  String get propertyTitleTooShort {
    return Intl.message(
      'Property title must be at least 3 characters',
      name: 'propertyTitleTooShort',
      desc: '',
      args: [],
    );
  }

  /// `Property description is required`
  String get propertyDescriptionRequired {
    return Intl.message(
      'Property description is required',
      name: 'propertyDescriptionRequired',
      desc: '',
      args: [],
    );
  }

  /// `Property description must be at least 10 characters`
  String get propertyDescriptionTooShort {
    return Intl.message(
      'Property description must be at least 10 characters',
      name: 'propertyDescriptionTooShort',
      desc: '',
      args: [],
    );
  }

  /// `Price is required`
  String get priceRequired {
    return Intl.message(
      'Price is required',
      name: 'priceRequired',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a valid price`
  String get priceInvalid {
    return Intl.message(
      'Please enter a valid price',
      name: 'priceInvalid',
      desc: '',
      args: [],
    );
  }

  /// `Minimum price is 50 SAR per night`
  String get priceMinimum {
    return Intl.message(
      'Minimum price is 50 SAR per night',
      name: 'priceMinimum',
      desc: '',
      args: [],
    );
  }

  /// `Please select a category`
  String get categoryRequired {
    return Intl.message(
      'Please select a category',
      name: 'categoryRequired',
      desc: '',
      args: [],
    );
  }

  /// `Please select a property type`
  String get propertyTypeRequired {
    return Intl.message(
      'Please select a property type',
      name: 'propertyTypeRequired',
      desc: '',
      args: [],
    );
  }

  /// `Please select a cancellation policy`
  String get cancellationPolicyRequired {
    return Intl.message(
      'Please select a cancellation policy',
      name: 'cancellationPolicyRequired',
      desc: '',
      args: [],
    );
  }

  /// `Number of guests is required`
  String get guestsRequired {
    return Intl.message(
      'Number of guests is required',
      name: 'guestsRequired',
      desc: '',
      args: [],
    );
  }

  /// `Number of guests must be between 1 and 20`
  String get guestsInvalid {
    return Intl.message(
      'Number of guests must be between 1 and 20',
      name: 'guestsInvalid',
      desc: '',
      args: [],
    );
  }

  /// `Number of bedrooms is required`
  String get bedsRequired {
    return Intl.message(
      'Number of bedrooms is required',
      name: 'bedsRequired',
      desc: '',
      args: [],
    );
  }

  /// `Number of bedrooms must be between 1 and 10`
  String get bedsInvalid {
    return Intl.message(
      'Number of bedrooms must be between 1 and 10',
      name: 'bedsInvalid',
      desc: '',
      args: [],
    );
  }

  /// `Number of bathrooms is required`
  String get bathsRequired {
    return Intl.message(
      'Number of bathrooms is required',
      name: 'bathsRequired',
      desc: '',
      args: [],
    );
  }

  /// `Number of bathrooms must be between 1 and 10`
  String get bathsInvalid {
    return Intl.message(
      'Number of bathrooms must be between 1 and 10',
      name: 'bathsInvalid',
      desc: '',
      args: [],
    );
  }

  /// `Please select at least one facility`
  String get facilitiesRequired {
    return Intl.message(
      'Please select at least one facility',
      name: 'facilitiesRequired',
      desc: '',
      args: [],
    );
  }

  /// `Please select a location`
  String get locationRequired {
    return Intl.message(
      'Please select a location',
      name: 'locationRequired',
      desc: '',
      args: [],
    );
  }

  /// `Please add at least one image`
  String get imagesRequired {
    return Intl.message(
      'Please add at least one image',
      name: 'imagesRequired',
      desc: '',
      args: [],
    );
  }

  /// `Please add at least 3 images`
  String get imagesMinimum {
    return Intl.message(
      'Please add at least 3 images',
      name: 'imagesMinimum',
      desc: '',
      args: [],
    );
  }

  /// `Category & Type`
  String get categoryAndType {
    return Intl.message(
      'Category & Type',
      name: 'categoryAndType',
      desc: '',
      args: [],
    );
  }

  /// `Location & Address`
  String get locationAndAddress {
    return Intl.message(
      'Location & Address',
      name: 'locationAndAddress',
      desc: '',
      args: [],
    );
  }

  /// `Photos & Video`
  String get photosAndVideo {
    return Intl.message(
      'Photos & Video',
      name: 'photosAndVideo',
      desc: '',
      args: [],
    );
  }

  /// `Review & Submit`
  String get reviewAndSubmit {
    return Intl.message(
      'Review & Submit',
      name: 'reviewAndSubmit',
      desc: '',
      args: [],
    );
  }

  /// `Saving property...`
  String get savingProperty {
    return Intl.message(
      'Saving property...',
      name: 'savingProperty',
      desc: '',
      args: [],
    );
  }

  /// `Please fix the errors and try again`
  String get validationFailed {
    return Intl.message(
      'Please fix the errors and try again',
      name: 'validationFailed',
      desc: '',
      args: [],
    );
  }

  /// `Tell us about your property`
  String get basicInformationDesc {
    return Intl.message(
      'Tell us about your property',
      name: 'basicInformationDesc',
      desc: '',
      args: [],
    );
  }

  /// `Enter a catchy title for your property`
  String get propertyTitleHint {
    return Intl.message(
      'Enter a catchy title for your property',
      name: 'propertyTitleHint',
      desc: '',
      args: [],
    );
  }

  /// `Describe your property in detail`
  String get propertyDescriptionHint {
    return Intl.message(
      'Describe your property in detail',
      name: 'propertyDescriptionHint',
      desc: '',
      args: [],
    );
  }

  /// `100`
  String get priceHint {
    return Intl.message(
      '100',
      name: 'priceHint',
      desc: '',
      args: [],
    );
  }

  /// `Tip: Research similar properties in your area to set competitive pricing`
  String get priceGuidance {
    return Intl.message(
      'Tip: Research similar properties in your area to set competitive pricing',
      name: 'priceGuidance',
      desc: '',
      args: [],
    );
  }

  /// `Location Selected`
  String get locationSelected {
    return Intl.message(
      'Location Selected',
      name: 'locationSelected',
      desc: '',
      args: [],
    );
  }

  /// `Change Location`
  String get changeLocation {
    return Intl.message(
      'Change Location',
      name: 'changeLocation',
      desc: '',
      args: [],
    );
  }

  /// `Property Photos`
  String get propertyPhotos {
    return Intl.message(
      'Property Photos',
      name: 'propertyPhotos',
      desc: '',
      args: [],
    );
  }

  /// `Property Video (Optional)`
  String get propertyVideoOptional {
    return Intl.message(
      'Property Video (Optional)',
      name: 'propertyVideoOptional',
      desc: '',
      args: [],
    );
  }

  /// `Add Photos`
  String get addPhotos {
    return Intl.message(
      'Add Photos',
      name: 'addPhotos',
      desc: '',
      args: [],
    );
  }

  /// `Add Video`
  String get addVideo {
    return Intl.message(
      'Add Video',
      name: 'addVideo',
      desc: '',
      args: [],
    );
  }

  /// `Change Video`
  String get changeVideo {
    return Intl.message(
      'Change Video',
      name: 'changeVideo',
      desc: '',
      args: [],
    );
  }

  /// `Take Photo`
  String get takePhoto {
    return Intl.message(
      'Take Photo',
      name: 'takePhoto',
      desc: '',
      args: [],
    );
  }

  /// `Choose from Gallery`
  String get chooseFromGallery {
    return Intl.message(
      'Choose from Gallery',
      name: 'chooseFromGallery',
      desc: '',
      args: [],
    );
  }

  /// `Choose Multiple`
  String get chooseMultiple {
    return Intl.message(
      'Choose Multiple',
      name: 'chooseMultiple',
      desc: '',
      args: [],
    );
  }

  /// `No photos added yet`
  String get noPhotosAdded {
    return Intl.message(
      'No photos added yet',
      name: 'noPhotosAdded',
      desc: '',
      args: [],
    );
  }

  /// `Video Preview`
  String get videoPreview {
    return Intl.message(
      'Video Preview',
      name: 'videoPreview',
      desc: '',
      args: [],
    );
  }

  /// `Main`
  String get main {
    return Intl.message(
      'Main',
      name: 'main',
      desc: '',
      args: [],
    );
  }

  /// `Previous`
  String get previous {
    return Intl.message(
      'Previous',
      name: 'previous',
      desc: '',
      args: [],
    );
  }

  /// `Not provided`
  String get notProvided {
    return Intl.message(
      'Not provided',
      name: 'notProvided',
      desc: '',
      args: [],
    );
  }

  /// `Creating your property...`
  String get creatingProperty {
    return Intl.message(
      'Creating your property...',
      name: 'creatingProperty',
      desc: '',
      args: [],
    );
  }

  /// `Please wait while we process your information`
  String get pleaseWaitProcessing {
    return Intl.message(
      'Please wait while we process your information',
      name: 'pleaseWaitProcessing',
      desc: '',
      args: [],
    );
  }

  /// `Choose the category and type that best describes your property`
  String get categoryTypeDescription {
    return Intl.message(
      'Choose the category and type that best describes your property',
      name: 'categoryTypeDescription',
      desc: '',
      args: [],
    );
  }

  /// `Set booking rules and provide tourism permit information`
  String get bookingRulesDescription {
    return Intl.message(
      'Set booking rules and provide tourism permit information',
      name: 'bookingRulesDescription',
      desc: '',
      args: [],
    );
  }

  /// `Enter any specific rules for booking your property (optional)`
  String get bookingRulesHint {
    return Intl.message(
      'Enter any specific rules for booking your property (optional)',
      name: 'bookingRulesHint',
      desc: '',
      args: [],
    );
  }

  /// `Enter your tourism permit number (required)`
  String get tourismPermitNumberHint {
    return Intl.message(
      'Enter your tourism permit number (required)',
      name: 'tourismPermitNumberHint',
      desc: '',
      args: [],
    );
  }

  /// `Tourism Permit Document *`
  String get tourismPermitDocument {
    return Intl.message(
      'Tourism Permit Document *',
      name: 'tourismPermitDocument',
      desc: '',
      args: [],
    );
  }

  /// `Upload your tourism permit document (PDF, DOC, DOCX, JPG, PNG)`
  String get tourismPermitDocumentHint {
    return Intl.message(
      'Upload your tourism permit document (PDF, DOC, DOCX, JPG, PNG)',
      name: 'tourismPermitDocumentHint',
      desc: '',
      args: [],
    );
  }

  /// `Document Selected`
  String get documentSelected {
    return Intl.message(
      'Document Selected',
      name: 'documentSelected',
      desc: '',
      args: [],
    );
  }

  /// `Change Document`
  String get changeDocument {
    return Intl.message(
      'Change Document',
      name: 'changeDocument',
      desc: '',
      args: [],
    );
  }

  /// `Upload Document`
  String get uploadDocument {
    return Intl.message(
      'Upload Document',
      name: 'uploadDocument',
      desc: '',
      args: [],
    );
  }

  /// `Tourism permit is required for all properties. This helps build trust with guests and ensures compliance with local regulations.`
  String get tourismPermitInfo {
    return Intl.message(
      'Tourism permit is required for all properties. This helps build trust with guests and ensures compliance with local regulations.',
      name: 'tourismPermitInfo',
      desc: '',
      args: [],
    );
  }

  /// `Specify the details and amenities of your property`
  String get propertyDetailsDescription {
    return Intl.message(
      'Specify the details and amenities of your property',
      name: 'propertyDetailsDescription',
      desc: '',
      args: [],
    );
  }

  /// `Tourism permit number is required`
  String get tourismPermitNumberRequired {
    return Intl.message(
      'Tourism permit number is required',
      name: 'tourismPermitNumberRequired',
      desc: '',
      args: [],
    );
  }

  /// `Tourism permit number should be at least 5 characters`
  String get tourismPermitNumberMinLength {
    return Intl.message(
      'Tourism permit number should be at least 5 characters',
      name: 'tourismPermitNumberMinLength',
      desc: '',
      args: [],
    );
  }

  /// `Tourism permit document is required`
  String get tourismPermitDocumentRequired {
    return Intl.message(
      'Tourism permit document is required',
      name: 'tourismPermitDocumentRequired',
      desc: '',
      args: [],
    );
  }

  /// `Booking rules should be at least 10 characters if provided`
  String get bookingRulesMinLength {
    return Intl.message(
      'Booking rules should be at least 10 characters if provided',
      name: 'bookingRulesMinLength',
      desc: '',
      args: [],
    );
  }

  /// `Review Details`
  String get reviewDetails {
    return Intl.message(
      'Review Details',
      name: 'reviewDetails',
      desc: '',
      args: [],
    );
  }

  /// `Title`
  String get title {
    return Intl.message(
      'Title',
      name: 'title',
      desc: '',
      args: [],
    );
  }

  /// `Booking Rules`
  String get bookingRulesReview {
    return Intl.message(
      'Booking Rules',
      name: 'bookingRulesReview',
      desc: '',
      args: [],
    );
  }

  /// `Tourism Permit Number`
  String get tourismPermitNumberReview {
    return Intl.message(
      'Tourism Permit Number',
      name: 'tourismPermitNumberReview',
      desc: '',
      args: [],
    );
  }

  /// `Tourism Permit Document`
  String get tourismPermitDocumentReview {
    return Intl.message(
      'Tourism Permit Document',
      name: 'tourismPermitDocumentReview',
      desc: '',
      args: [],
    );
  }

  /// `Photos`
  String get photos {
    return Intl.message(
      'Photos',
      name: 'photos',
      desc: '',
      args: [],
    );
  }

  /// `Not selected`
  String get notSelected {
    return Intl.message(
      'Not selected',
      name: 'notSelected',
      desc: '',
      args: [],
    );
  }

  /// `None selected`
  String get noneSelected {
    return Intl.message(
      'None selected',
      name: 'noneSelected',
      desc: '',
      args: [],
    );
  }

  /// `Unknown`
  String get unknown {
    return Intl.message(
      'Unknown',
      name: 'unknown',
      desc: '',
      args: [],
    );
  }

  /// `Selected`
  String get selected {
    return Intl.message(
      'Selected',
      name: 'selected',
      desc: '',
      args: [],
    );
  }

  /// `Uploaded`
  String get uploaded {
    return Intl.message(
      'Uploaded',
      name: 'uploaded',
      desc: '',
      args: [],
    );
  }

  /// `Not uploaded`
  String get notUploaded {
    return Intl.message(
      'Not uploaded',
      name: 'notUploaded',
      desc: '',
      args: [],
    );
  }

  /// `Added`
  String get added {
    return Intl.message(
      'Added',
      name: 'added',
      desc: '',
      args: [],
    );
  }

  /// `Not added`
  String get notAdded {
    return Intl.message(
      'Not added',
      name: 'notAdded',
      desc: '',
      args: [],
    );
  }

  /// `images`
  String get images {
    return Intl.message(
      'images',
      name: 'images',
      desc: '',
      args: [],
    );
  }

  /// `Add Photo`
  String get addPhoto {
    return Intl.message(
      'Add Photo',
      name: 'addPhoto',
      desc: '',
      args: [],
    );
  }

  /// `Select Facilities`
  String get selectFacilities {
    return Intl.message(
      'Select Facilities',
      name: 'selectFacilities',
      desc: '',
      args: [],
    );
  }

  /// `Failed to load property types`
  String get failedToLoadPropertyTypes {
    return Intl.message(
      'Failed to load property types',
      name: 'failedToLoadPropertyTypes',
      desc: '',
      args: [],
    );
  }

  /// `Failed to load cancellation policies`
  String get failedToLoadCancellationPolicies {
    return Intl.message(
      'Failed to load cancellation policies',
      name: 'failedToLoadCancellationPolicies',
      desc: '',
      args: [],
    );
  }

  /// `Loading categories...`
  String get loadingCategories {
    return Intl.message(
      'Loading categories...',
      name: 'loadingCategories',
      desc: '',
      args: [],
    );
  }

  /// `Loading property types...`
  String get loadingPropertyTypes {
    return Intl.message(
      'Loading property types...',
      name: 'loadingPropertyTypes',
      desc: '',
      args: [],
    );
  }

  /// `Loading cancellation policies...`
  String get loadingCancellationPolicies {
    return Intl.message(
      'Loading cancellation policies...',
      name: 'loadingCancellationPolicies',
      desc: '',
      args: [],
    );
  }

  /// `Loading facilities...`
  String get loadingFacilities {
    return Intl.message(
      'Loading facilities...',
      name: 'loadingFacilities',
      desc: '',
      args: [],
    );
  }

  /// `Property type option`
  String get propertyTypeOption {
    return Intl.message(
      'Property type option',
      name: 'propertyTypeOption',
      desc: '',
      args: [],
    );
  }

  /// `Friends`
  String get friends {
    return Intl.message(
      'Friends',
      name: 'friends',
      desc: '',
      args: [],
    );
  }

  /// `Requests`
  String get requests {
    return Intl.message(
      'Requests',
      name: 'requests',
      desc: '',
      args: [],
    );
  }

  /// `Search`
  String get searchFriends {
    return Intl.message(
      'Search',
      name: 'searchFriends',
      desc: '',
      args: [],
    );
  }

  /// `Search for friends by name or email`
  String get searchFriendsHint {
    return Intl.message(
      'Search for friends by name or email',
      name: 'searchFriendsHint',
      desc: '',
      args: [],
    );
  }

  /// `Search for friends`
  String get searchForFriends {
    return Intl.message(
      'Search for friends',
      name: 'searchForFriends',
      desc: '',
      args: [],
    );
  }

  /// `Type a name or email to search for new friends`
  String get searchForFriendsDescription {
    return Intl.message(
      'Type a name or email to search for new friends',
      name: 'searchForFriendsDescription',
      desc: '',
      args: [],
    );
  }

  /// `No friends yet`
  String get noFriendsYet {
    return Intl.message(
      'No friends yet',
      name: 'noFriendsYet',
      desc: '',
      args: [],
    );
  }

  /// `Start by adding new friends to connect with them`
  String get noFriendsDescription {
    return Intl.message(
      'Start by adding new friends to connect with them',
      name: 'noFriendsDescription',
      desc: '',
      args: [],
    );
  }

  /// `No pending requests`
  String get noPendingRequests {
    return Intl.message(
      'No pending requests',
      name: 'noPendingRequests',
      desc: '',
      args: [],
    );
  }

  /// `No friend requests are waiting`
  String get noPendingRequestsDescription {
    return Intl.message(
      'No friend requests are waiting',
      name: 'noPendingRequestsDescription',
      desc: '',
      args: [],
    );
  }

  /// `No users found with this name`
  String get noSearchResultsDescription {
    return Intl.message(
      'No users found with this name',
      name: 'noSearchResultsDescription',
      desc: '',
      args: [],
    );
  }

  /// `Add`
  String get addFriend {
    return Intl.message(
      'Add',
      name: 'addFriend',
      desc: '',
      args: [],
    );
  }

  /// `Accept`
  String get acceptRequest {
    return Intl.message(
      'Accept',
      name: 'acceptRequest',
      desc: '',
      args: [],
    );
  }

  /// `Decline`
  String get declineRequest {
    return Intl.message(
      'Decline',
      name: 'declineRequest',
      desc: '',
      args: [],
    );
  }

  /// `Remove`
  String get removeFriend {
    return Intl.message(
      'Remove',
      name: 'removeFriend',
      desc: '',
      args: [],
    );
  }

  /// `Friends`
  String get alreadyFriends {
    return Intl.message(
      'Friends',
      name: 'alreadyFriends',
      desc: '',
      args: [],
    );
  }

  /// `Sent`
  String get requestSent {
    return Intl.message(
      'Sent',
      name: 'requestSent',
      desc: '',
      args: [],
    );
  }

  /// `Host`
  String get host {
    return Intl.message(
      'Host',
      name: 'host',
      desc: '',
      args: [],
    );
  }

  /// `mutual friends`
  String get mutualFriends {
    return Intl.message(
      'mutual friends',
      name: 'mutualFriends',
      desc: '',
      args: [],
    );
  }

  /// `Friend request sent successfully`
  String get friendRequestSentSuccess {
    return Intl.message(
      'Friend request sent successfully',
      name: 'friendRequestSentSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Error sending friend request`
  String get friendRequestSentError {
    return Intl.message(
      'Error sending friend request',
      name: 'friendRequestSentError',
      desc: '',
      args: [],
    );
  }

  /// `Friend request accepted successfully`
  String get friendRequestAcceptedSuccess {
    return Intl.message(
      'Friend request accepted successfully',
      name: 'friendRequestAcceptedSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Error accepting friend request`
  String get friendRequestAcceptedError {
    return Intl.message(
      'Error accepting friend request',
      name: 'friendRequestAcceptedError',
      desc: '',
      args: [],
    );
  }

  /// `Friend request declined successfully`
  String get friendRequestDeclinedSuccess {
    return Intl.message(
      'Friend request declined successfully',
      name: 'friendRequestDeclinedSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Error declining friend request`
  String get friendRequestDeclinedError {
    return Intl.message(
      'Error declining friend request',
      name: 'friendRequestDeclinedError',
      desc: '',
      args: [],
    );
  }

  /// `Friend removed successfully`
  String get friendRemovedSuccess {
    return Intl.message(
      'Friend removed successfully',
      name: 'friendRemovedSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Error removing friend`
  String get friendRemovedError {
    return Intl.message(
      'Error removing friend',
      name: 'friendRemovedError',
      desc: '',
      args: [],
    );
  }

  /// `Search error`
  String get searchError {
    return Intl.message(
      'Search error',
      name: 'searchError',
      desc: '',
      args: [],
    );
  }

  /// `Loading friends...`
  String get loadingFriends {
    return Intl.message(
      'Loading friends...',
      name: 'loadingFriends',
      desc: '',
      args: [],
    );
  }

  /// `Loading requests...`
  String get loadingRequests {
    return Intl.message(
      'Loading requests...',
      name: 'loadingRequests',
      desc: '',
      args: [],
    );
  }

  /// `Refresh friends list`
  String get refreshFriends {
    return Intl.message(
      'Refresh friends list',
      name: 'refreshFriends',
      desc: '',
      args: [],
    );
  }

  /// `Please go to the requests tab to accept the friend request`
  String get goToPendingRequests {
    return Intl.message(
      'Please go to the requests tab to accept the friend request',
      name: 'goToPendingRequests',
      desc: '',
      args: [],
    );
  }

  /// `Messaging feature is under development`
  String get messageFeatureInDevelopment {
    return Intl.message(
      'Messaging feature is under development',
      name: 'messageFeatureInDevelopment',
      desc: '',
      args: [],
    );
  }

  /// `Failed to load friends`
  String get failedToLoadFriends {
    return Intl.message(
      'Failed to load friends',
      name: 'failedToLoadFriends',
      desc: '',
      args: [],
    );
  }

  /// `Failed to load requests`
  String get failedToLoadRequests {
    return Intl.message(
      'Failed to load requests',
      name: 'failedToLoadRequests',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'ar'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
