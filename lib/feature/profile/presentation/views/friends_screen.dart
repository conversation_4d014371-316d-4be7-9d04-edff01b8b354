import 'package:flutter/material.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/feature/profile/data/services/friends_api_service.dart';
import 'package:gather_point/feature/profile/data/models/friend_model.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:gather_point/generated/l10n.dart';

class FriendsScreen extends StatefulWidget {
  const FriendsScreen({super.key});

  @override
  State<FriendsScreen> createState() => _FriendsScreenState();
}

class _FriendsScreenState extends State<FriendsScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final FriendsApiService _friendsApiService = getIt<FriendsApiService>();
  final TextEditingController _searchController = TextEditingController();

  List<FriendModel> _friends = [];
  List<FriendRequestModel> _pendingRequests = [];
  List<SearchUserModel> _searchResults = [];
  bool _isLoading = true;
  bool _isSearching = false;
  String? _error;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _searchController.addListener(() {
      setState(() {}); // Update UI when search text changes
    });
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final friendsResult = await _friendsApiService.getFriends();
      final requestsResult = await _friendsApiService.getPendingRequests();
      
      if (mounted) {
        setState(() {
          _friends = friendsResult;
          _pendingRequests = requestsResult;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return EnhancedPageLayout(
      title: 'المعارف', // TODO: Use S.of(context).acquaintances when available
      showBackButton: true,
      actions: [
        IconButton(
          icon: const Icon(Icons.person_add_outlined),
          onPressed: () => _showSearchDialog(),
          tooltip: S.of(context).addFriend,
        ),
      ],
      body: Column(
        children: [
          // Tab Bar
          Container(
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: context.cardColor,
              borderRadius: BorderRadius.circular(25),
              border: Border.all(color: context.secondaryTextColor.withValues(alpha: 0.2)),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                color: context.accentColor,
                borderRadius: BorderRadius.circular(25),
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              indicatorPadding: const EdgeInsets.all(4),
              labelColor: Colors.black,
              unselectedLabelColor: context.primaryTextColor,
              labelStyle: AppTextStyles.font14SemiBold,
              unselectedLabelStyle: AppTextStyles.font14Regular,
              dividerColor: Colors.transparent,
              tabs: [
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(S.of(context).friends),
                      if (_friends.isNotEmpty) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '${_friends.length}',
                            style: AppTextStyles.font12Bold.copyWith(
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(S.of(context).requests),
                      if (_pendingRequests.isNotEmpty) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '${_pendingRequests.length}',
                            style: AppTextStyles.font12Bold.copyWith(
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Tab(text: S.of(context).searchFriends),
              ],
            ),
          ),
          
          // Tab Views
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildFriendsTab(),
                _buildRequestsTab(),
                _buildSearchTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFriendsTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return _buildErrorWidget();
    }

    if (_friends.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: context.secondaryTextColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              S.of(context).noFriendsYet,
              style: AppTextStyles.font18Bold.copyWith(
                color: context.primaryTextColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              S.of(context).noFriendsDescription,
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _showSearchDialog(),
              icon: const Icon(Icons.person_add),
              label: Text(S.of(context).addFriend),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 90), // Add bottom padding for nav bar (70px + 20px extra)
        itemCount: _friends.length,
        itemBuilder: (context, index) {
          final friend = _friends[index];
          return _buildFriendCard(friend);
        },
      ),
    );
  }

  Widget _buildRequestsTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_pendingRequests.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 64,
              color: context.secondaryTextColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              S.of(context).noPendingRequests,
              style: AppTextStyles.font18Bold.copyWith(
                color: context.primaryTextColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ستظهر هنا طلبات الصداقة الواردة', // TODO: Use S.of(context).noPendingRequestsDescription when available
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 90), // Add bottom padding for nav bar (70px + 20px extra)
        itemCount: _pendingRequests.length,
        itemBuilder: (context, index) {
          final request = _pendingRequests[index];
          return _buildRequestCard(request);
        },
      ),
    );
  }

  Widget _buildSearchTab() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 90), // Add bottom padding for nav bar (70px + 20px extra)
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: S.of(context).searchFriendsHint,
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _searchUsers('');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: (query) => _searchUsers(query),
            onSubmitted: (query) => _searchUsers(query),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: _buildSearchContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchContent() {
    if (_isSearching) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_searchQuery.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search,
              size: 64,
              color: context.secondaryTextColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              S.of(context).searchForFriends,
              style: AppTextStyles.font18Bold.copyWith(
                color: context.primaryTextColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              S.of(context).searchForFriendsDescription,
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.person_search,
              size: 64,
              color: context.secondaryTextColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              S.of(context).noSearchResults,
              style: AppTextStyles.font18Bold.copyWith(
                color: context.primaryTextColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              S.of(context).noSearchResultsDescription,
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final user = _searchResults[index];
        return _buildSearchUserCard(user);
      },
    );
  }

  Widget _buildFriendCard(FriendModel friend) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: EnhancedCard(
        child: Row(
          children: [
            // Profile Image
            CircleAvatar(
              radius: 30,
              backgroundImage: friend.profilePhotoUrl != null
                  ? CachedNetworkImageProvider(friend.profilePhotoUrl!)
                  : null,
              child: friend.profilePhotoUrl == null
                  ? Icon(Icons.person, size: 30, color: Colors.grey.shade400)
                  : null,
            ),
            const SizedBox(width: 12),
            
            // Friend Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    friend.fullName,
                    style: AppTextStyles.font16SemiBold.copyWith(
                      color: context.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  if (friend.isHoster)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: context.accentColor.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        S.of(context).host,
                        style: AppTextStyles.font12SemiBold.copyWith(
                          color: context.accentColor,
                        ),
                      ),
                    ),
                  const SizedBox(height: 4),
                  Text(
                    '${S.of(context).alreadyFriends} ${S.of(context).since} ${friend.friendsSince}',
                    style: AppTextStyles.font12Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                  if (friend.mutualFriendsCount > 0)
                    Text(
                      '${friend.mutualFriendsCount} ${S.of(context).mutualFriends}',
                      style: AppTextStyles.font12Regular.copyWith(
                        color: context.secondaryTextColor,
                      ),
                    ),
                ],
              ),
            ),
            
            // Actions
            PopupMenuButton<String>(
              onSelected: (value) => _handleFriendAction(value, friend),
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'message',
                  child: Row(
                    children: [
                      const Icon(Icons.message_outlined),
                      const SizedBox(width: 8),
                      Text('إرسال رسالة'), // TODO: Use S.of(context).sendMessage when available
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'remove',
                  child: Row(
                    children: [
                      const Icon(Icons.person_remove_outlined, color: Colors.red),
                      const SizedBox(width: 8),
                      Text('إزالة صديق', style: const TextStyle(color: Colors.red)), // TODO: Use S.of(context).removeFriend when available
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRequestCard(FriendRequestModel request) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: EnhancedCard(
        child: Column(
          children: [
            Row(
              children: [
                // Profile Image
                CircleAvatar(
                  radius: 25,
                  backgroundImage: request.user.profilePhotoUrl != null
                      ? CachedNetworkImageProvider(request.user.profilePhotoUrl!)
                      : null,
                  child: request.user.profilePhotoUrl == null
                      ? Icon(Icons.person, size: 25, color: Colors.grey.shade400)
                      : null,
                ),
                const SizedBox(width: 12),
                
                // User Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        request.user.fullName,
                        style: AppTextStyles.font16SemiBold.copyWith(
                          color: context.primaryTextColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      if (request.user.isHoster)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: context.accentColor.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            S.of(context).host,
                            style: AppTextStyles.font12SemiBold.copyWith(
                              color: context.accentColor,
                            ),
                          ),
                        ),
                      const SizedBox(height: 4),
                      Text(
                        'طلب الصداقة ${_formatRequestTime(request.requestedAt)}', // TODO: Use S.of(context).friendRequestTime when available
                        style: AppTextStyles.font12Regular.copyWith(
                          color: context.secondaryTextColor,
                        ),
                      ),
                      if (request.mutualFriendsCount > 0)
                        Text(
                          '${request.mutualFriendsCount} ${S.of(context).mutualFriends}',
                          style: AppTextStyles.font12Regular.copyWith(
                            color: context.secondaryTextColor,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _acceptRequest(request),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.accentColor,
                      foregroundColor: Colors.black,
                    ),
                    child: Text(S.of(context).acceptRequest),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _declineRequest(request),
                    child: Text(S.of(context).declineRequest),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchUserCard(SearchUserModel user) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Profile Image
            CircleAvatar(
              radius: 30,
              backgroundImage: user.profilePhotoUrl != null
                  ? NetworkImage(user.profilePhotoUrl!)
                  : null,
              child: user.profilePhotoUrl == null
                  ? Text(
                      user.fullName.isNotEmpty ? user.fullName[0].toUpperCase() : 'U',
                      style: AppTextStyles.font18Bold.copyWith(
                        color: Colors.white,
                      ),
                    )
                  : null,
            ),
            const SizedBox(width: 16),

            // User Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user.fullName,
                    style: AppTextStyles.font16Bold.copyWith(
                      color: context.primaryTextColor,
                    ),
                  ),
                  if (user.email.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      user.email,
                      style: AppTextStyles.font14Regular.copyWith(
                        color: context.secondaryTextColor,
                      ),
                    ),
                  ],
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      if (user.isHoster)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: context.accentColor.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            S.of(context).host,
                            style: AppTextStyles.font12Regular.copyWith(
                              color: context.accentColor,
                            ),
                          ),
                        ),
                      if (user.isHoster && user.mutualFriendsCount > 0)
                        const SizedBox(width: 8),
                      if (user.mutualFriendsCount > 0)
                        Text(
                          '${user.mutualFriendsCount} ${S.of(context).mutualFriends}',
                          style: AppTextStyles.font12Regular.copyWith(
                            color: context.secondaryTextColor,
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),

            // Action Button based on friendship status
            _buildFriendshipActionButton(user),
          ],
        ),
      ),
    );
  }

  Widget _buildFriendshipActionButton(SearchUserModel user) {
    switch (user.friendshipStatus) {
      case 'friends':
        return OutlinedButton(
          onPressed: null, // Already friends
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          child: Text(S.of(context).alreadyFriends),
        );
      case 'request_sent':
        return OutlinedButton(
          onPressed: null, // Request already sent
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          child: Text(S.of(context).requestSent),
        );
      case 'request_received':
        return ElevatedButton(
          onPressed: () => _acceptFriendRequest(user),
          style: ElevatedButton.styleFrom(
            backgroundColor: context.accentColor,
            foregroundColor: Colors.black,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          child: Text(S.of(context).acceptRequest),
        );
      case 'none':
      default:
        return ElevatedButton(
          onPressed: () => _sendFriendRequest(user),
          style: ElevatedButton.styleFrom(
            backgroundColor: context.accentColor,
            foregroundColor: Colors.black,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          child: Text(S.of(context).addFriend),
        );
    }
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red.shade300,
          ),
          const SizedBox(height: 16),
          Text(
            S.of(context).failedToLoadFriends,
            style: AppTextStyles.font16SemiBold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: AppTextStyles.font14Regular.copyWith(
              color: context.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('إعادة المحاولة'), // TODO: Use S.of(context).retryButton when available
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    // Navigate to search tab
    _tabController.animateTo(2);
    _searchController.clear();
    setState(() {
      _searchResults.clear();
      _searchQuery = '';
    });
  }

  Future<void> _searchUsers(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults.clear();
        _searchQuery = '';
      });
      return;
    }

    setState(() {
      _isSearching = true;
      _searchQuery = query.trim();
    });

    try {
      final results = await _friendsApiService.searchUsers(query.trim());
      if (mounted) {
        setState(() {
          _searchResults = results;
          _isSearching = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSearching = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${S.of(context).searchError}: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _sendFriendRequest(SearchUserModel user) async {
    try {
      await _friendsApiService.sendFriendRequest(user.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(S.of(context).friendRequestSentSuccess)),
        );
        // Refresh search results to update friendship status
        _searchUsers(_searchQuery);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${S.of(context).friendRequestSentError}: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _acceptFriendRequest(SearchUserModel user) async {
    try {
      // Note: We need the friendship_id, but SearchUserModel doesn't have it
      // This is a limitation - we'd need to modify the API response to include it
      // For now, we'll show a message to refresh the pending requests tab
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(S.of(context).goToPendingRequests)),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${S.of(context).friendRequestAcceptedError}: ${e.toString()}')),
        );
      }
    }
  }

  void _handleFriendAction(String action, FriendModel friend) {
    switch (action) {
      case 'message':
        // TODO: Navigate to chat
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(S.of(context).messageFeatureInDevelopment)),
        );
        break;
      case 'remove':
        _removeFriend(friend);
        break;
    }
  }

  Future<void> _acceptRequest(FriendRequestModel request) async {
    try {
      await _friendsApiService.acceptRequest(request.friendshipId);
      _loadData(); // Refresh data
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).friendRequestAcceptedSuccess),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${S.of(context).friendRequestAcceptedError}: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _declineRequest(FriendRequestModel request) async {
    try {
      await _friendsApiService.declineRequest(request.friendshipId);
      _loadData(); // Refresh data
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(S.of(context).friendRequestDeclinedSuccess)),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${S.of(context).friendRequestDeclinedError}: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _removeFriend(FriendModel friend) async {
    try {
      await _friendsApiService.removeFriend(friend.id);
      _loadData(); // Refresh data
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(S.of(context).friendRemovedSuccess)),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${S.of(context).friendRemovedError}: ${e.toString()}')),
        );
      }
    }
  }

  String _formatRequestTime(String dateTime) {
    try {
      final date = DateTime.parse(dateTime);
      final now = DateTime.now();
      final difference = now.difference(date);

      if (difference.inMinutes < 60) {
        return 'منذ ${difference.inMinutes} دقيقة'; // TODO: Use S.of(context) translations when available
      } else if (difference.inHours < 24) {
        return 'منذ ${difference.inHours} ساعة'; // TODO: Use S.of(context) translations when available
      } else {
        return 'منذ ${difference.inDays} يوم'; // TODO: Use S.of(context) translations when available
      }
    } catch (e) {
      return dateTime;
    }
  }
}
