import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/profile/presentation/cubit/profile_cubit.dart';
import 'package:gather_point/feature/profile/presentation/cubit/profile_state.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:go_router/go_router.dart';

class AccountSettingsScreen extends StatelessWidget {
  const AccountSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    return EnhancedPageLayout(
      title: 'إعدادات الحساب',
      showBackButton: true,
      body: BlocBuilder<ProfileCubit, ProfileState>(
        builder: (context, state) {
          UserEntity? user;

          if (state is ProfileLoaded || state is ProfileUpdated || state is ProfileHosterModeToggled) {
            if (state is ProfileLoaded) user = state.user;
            if (state is ProfileUpdated) user = state.user;
            if (state is ProfileHosterModeToggled) user = state.user;
          }
          
          if (user == null || user.isGuest) {
            return const Center(
              child: Text('يجب تسجيل الدخول للوصول إلى إعدادات الحساب'),
            );
          }
          
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Account Information Section
                _AccountInfoSection(user: user),
                const SizedBox(height: 16),
                
                // Security Section
                const _SecuritySection(),
                const SizedBox(height: 16),

                // Preferences Section
                const _PreferencesSection(),
                const SizedBox(height: 16),

                // Danger Zone
                const _DangerZoneSection(),
              ],
            ),
          );
        },
      ),
    );
  }
}

class _AccountInfoSection extends StatelessWidget {
  final UserEntity user;
  
  const _AccountInfoSection({required this.user});

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات الحساب',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _SettingsItem(
            icon: Icons.person_outline,
            title: 'تعديل الملف الشخصي',
            subtitle: 'تحديث الاسم والصورة والمعلومات الشخصية',
            onTap: () {
              context.push('/edit-profile', extra: {'user': user});
            },
          ),
          
          _SettingsItem(
            icon: Icons.email_outlined,
            title: 'البريد الإلكتروني',
            subtitle: user.email,
            onTap: () {
              // Navigate to email change
            },
          ),
          
          _SettingsItem(
            icon: Icons.phone_outlined,
            title: 'رقم الهاتف',
            subtitle: user.phone ?? 'غير محدد',
            onTap: () {
              // Navigate to phone change
            },
          ),
        ],
      ),
    );
  }
}

class _SecuritySection extends StatelessWidget {
  const _SecuritySection();

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الأمان',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _SettingsItem(
            icon: Icons.lock_outline,
            title: 'تغيير كلمة المرور',
            subtitle: 'تحديث كلمة المرور الخاصة بك',
            onTap: () {
              // Navigate to password change
            },
          ),
          
          _SettingsItem(
            icon: Icons.security_outlined,
            title: 'المصادقة الثنائية',
            subtitle: 'تأمين إضافي لحسابك',
            onTap: () {
              // Navigate to 2FA settings
            },
          ),
          
          _SettingsItem(
            icon: Icons.devices_outlined,
            title: 'الأجهزة المتصلة',
            subtitle: 'إدارة الأجهزة التي تم تسجيل الدخول منها',
            onTap: () {
              // Navigate to connected devices
            },
          ),
        ],
      ),
    );
  }
}

class _PreferencesSection extends StatelessWidget {
  const _PreferencesSection();

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التفضيلات',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _SettingsItem(
            icon: Icons.language_outlined,
            title: 'اللغة',
            subtitle: 'العربية',
            onTap: () {
              // Navigate to language settings
            },
          ),
          
          _SettingsItem(
            icon: Icons.notifications_outlined,
            title: 'الإشعارات',
            subtitle: 'إدارة إعدادات الإشعارات',
            onTap: () {
              context.push('/notification-settings');
            },
          ),
          
          _SettingsItem(
            icon: Icons.location_on_outlined,
            title: 'الموقع',
            subtitle: 'إعدادات الخصوصية والموقع',
            onTap: () {
              // Navigate to location settings
            },
          ),
        ],
      ),
    );
  }
}

class _DangerZoneSection extends StatelessWidget {
  const _DangerZoneSection();

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'منطقة الخطر',
            style: AppTextStyles.font18Bold.copyWith(
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 16),
          
          _SettingsItem(
            icon: Icons.delete_forever_outlined,
            title: 'حذف الحساب',
            subtitle: 'حذف حسابك نهائياً - لا يمكن التراجع',
            onTap: () {
              _showDeleteAccountDialog(context);
            },
            isDestructive: true,
          ),
        ],
      ),
    );
  }
  
  void _showDeleteAccountDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الحساب'),
        content: const Text(
          'هل أنت متأكد من رغبتك في حذف حسابك؟ هذا الإجراء لا يمكن التراجع عنه وسيتم فقدان جميع بياناتك.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Implement account deletion
              context.read<ProfileCubit>().deleteAccount();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}

class _SettingsItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;
  final bool isDestructive;

  const _SettingsItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
    this.isDestructive = false,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
        child: Row(
          children: [
            Icon(
              icon,
              color: isDestructive ? Colors.red : context.secondaryTextColor,
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.font16Regular.copyWith(
                      color: isDestructive ? Colors.red : context.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: AppTextStyles.font12Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: context.secondaryTextColor,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
